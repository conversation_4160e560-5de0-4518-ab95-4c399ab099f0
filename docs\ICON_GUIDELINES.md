# 智慧养老项目图标使用指南

## 概述

本指南规定了智慧养老项目中图标的使用规范，包括命名约定、尺寸规格、颜色标准等，确保项目中图标使用的一致性和专业性。

## 图标类型

### 1. Emoji图标
- **用途**: 快速原型和临时使用
- **优点**: 无需额外文件，跨平台兼容性好
- **缺点**: 样式不可控，在不同设备上显示可能不一致

### 2. SVG图标
- **用途**: 生产环境推荐使用
- **优点**: 矢量格式，无损缩放，文件小，可自定义颜色
- **缺点**: 需要额外的文件管理

### 3. 图片图标
- **用途**: 复杂图标或品牌标识
- **优点**: 支持复杂设计和渐变效果
- **缺点**: 文件较大，缩放可能失真

## 命名规范

### 图标命名格式
```
{功能名称}-{样式}-{状态}
```

### 命名示例
- `home-line` - 首页线性图标
- `user-fill` - 用户填充图标
- `search-outline` - 搜索轮廓图标
- `heart-3-line` - 爱心线性图标（第3种变体）

### 样式后缀
- `line` - 线性图标（默认）
- `fill` - 填充图标
- `outline` - 轮廓图标
- `duotone` - 双色图标

### 状态后缀
- `active` - 激活状态
- `disabled` - 禁用状态
- `hover` - 悬停状态

## 尺寸规格

### 标准尺寸
- **小图标**: 24rpx (12px)
- **常规图标**: 32rpx (16px) - 默认尺寸
- **中等图标**: 48rpx (24px)
- **大图标**: 64rpx (32px)
- **特大图标**: 96rpx (48px)

### TabBar图标
- **尺寸**: 81x81px
- **格式**: SVG（推荐）或PNG
- **状态**: 普通状态和激活状态各一个

### 响应式尺寸
```scss
// 小屏幕
@media (max-width: 750rpx) {
  .icon-small { font-size: 20rpx; }
  .icon-normal { font-size: 28rpx; }
  .icon-large { font-size: 40rpx; }
}
```

## 颜色标准

### 主题色系
```scss
$primary-color: #ff8a00;      // 主色调 - 温暖橙色
$institution-color: #ff6b6b;  // 机构色 - 活力红色
$service-color: #4ecdc4;      // 服务色 - 清新青色
$elderly-color: #96ceb4;      // 适老色 - 温和绿色
$medical-color: #45b7d1;      // 医疗色 - 信任蓝色
$emergency-color: #e74c3c;    // 紧急色 - 警示红色
```

### 功能色彩
```scss
$icon-primary: #ff8a00;       // 主要图标
$icon-secondary: #666666;     // 次要图标
$icon-disabled: #cccccc;      // 禁用图标
$icon-success: #4cd964;       // 成功状态
$icon-warning: #f0ad4e;       // 警告状态
$icon-error: #dd524d;         // 错误状态
```

## 使用方法

### 基础用法
```vue
<!-- Emoji图标 -->
<Icon name="home-line" size="32rpx" color="#ff8a00" />

<!-- SVG图标 -->
<Icon name="building-line" type="svg" size="48rpx" />

<!-- 图片图标 -->
<Icon name="logo" type="image" src="/static/images/logo.png" size="64rpx" />
```

### 主题色快捷方式
```vue
<!-- 使用预定义主题色 -->
<Icon name="heart-3-line" primary />
<Icon name="building-line" institution />
<Icon name="search-line" service />
<Icon name="wheelchair-line" elderly />
<Icon name="emergency-line" emergency />
```

### 状态样式
```vue
<!-- 不同状态的图标 -->
<Icon name="notification-3-line" :class="{ active: isActive }" />
<Icon name="user-line" :class="{ disabled: !isEnabled }" />
```

## 图标分类

### 导航类 (Navigation)
- `home-line` - 首页
- `map-line` - 地图
- `search-line` - 搜索
- `menu-line` - 菜单
- `arrow-*-line` - 箭头系列

### 功能类 (Function)
- `building-line` - 建筑/机构
- `heart-3-line` - 关爱/健康
- `settings-line` - 设置
- `calendar-line` - 日历
- `time-line` - 时间

### 操作类 (Action)
- `add-line` - 添加
- `edit-line` - 编辑
- `delete-line` - 删除
- `share-line` - 分享
- `phone-line` - 电话

### 状态类 (Status)
- `check-line` - 成功
- `close-line` - 关闭
- `error-warning-line` - 警告
- `loading-line` - 加载

### 业务类 (Business)
- `nursing-home-line` - 养老院
- `meal-delivery-line` - 送餐服务
- `subsidy-line` - 补贴申请
- `assessment-line` - 能力评估

### 适老化类 (Elderly)
- `elderly-care-line` - 老人关怀
- `walking-stick-line` - 拐杖
- `hearing-aid-line` - 助听器
- `large-font-line` - 大字体

## 最佳实践

### 1. 一致性原则
- 同一功能在不同页面使用相同图标
- 保持图标风格统一
- 遵循既定的颜色规范

### 2. 可访问性
- 为图标提供文字说明
- 确保足够的对比度
- 支持适老化需求

### 3. 性能优化
- 优先使用SVG图标
- 合理控制图标数量
- 使用图标字体或雪碧图

### 4. 语义化
- 图标含义要直观明确
- 避免使用容易混淆的图标
- 考虑文化差异

## 文件组织

```
static/
├── icons/              # SVG图标文件
│   ├── home-line.svg
│   ├── building-line.svg
│   └── ...
├── tabbar/            # TabBar专用图标
│   ├── home.svg
│   ├── home-active.svg
│   └── ...
└── images/            # 复杂图片图标
    ├── logo.png
    └── ...
```

## 更新维护

### 添加新图标
1. 确定图标名称（遵循命名规范）
2. 创建SVG文件（81x81px）
3. 更新iconConfig.js配置
4. 测试在不同设备上的显示效果
5. 更新文档

### 图标优化
- 定期检查未使用的图标
- 优化SVG文件大小
- 更新过时的图标设计
- 收集用户反馈

## 工具推荐

### 设计工具
- Figma - 矢量图标设计
- Sketch - UI设计
- Adobe Illustrator - 专业矢量设计

### 优化工具
- SVGO - SVG优化
- ImageOptim - 图片压缩
- IconFont - 图标字体生成

---

*本指南会根据项目需求持续更新，请关注最新版本。*
