<template>
	<view class="container">
		<!-- 页面头部 -->
		<PageHeader title="领补贴">
			<template #actions>
				<InteractiveButton
					type="secondary"
					size="small"
					text="申请记录"
					icon="file-list-line"
					@click="viewApplications"
				></InteractiveButton>
			</template>
		</PageHeader>

		<!-- 补贴概览 -->
		<view class="subsidy-overview">
			<view class="overview-header">
				<text class="overview-title">补贴政策概览</text>
				<text class="overview-subtitle">为您提供最新的养老补贴政策信息</text>
			</view>
			<view class="stats-grid">
				<view class="stat-item" v-for="(stat, index) in subsidyStats" :key="index">
					<view class="stat-icon" :style="{ backgroundColor: stat.color }">
						<Icon :name="stat.icon" size="32rpx" color="#fff"></Icon>
					</view>
					<view class="stat-info">
						<text class="stat-value">{{ stat.value }}</text>
						<text class="stat-label">{{ stat.label }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 补贴分类 -->
		<view class="category-section">
			<text class="section-title">补贴分类</text>
			<scroll-view scroll-x="true" class="category-scroll">
				<view class="category-list">
					<view 
						v-for="(category, index) in subsidyCategories" 
						:key="index"
						class="category-item"
						:class="{ active: selectedCategory === category.value }"
						@click="selectCategory(category.value)"
					>
						<Icon :name="category.icon" size="32rpx" :color="selectedCategory === category.value ? '#fff' : '#ff8a00'"></Icon>
						<text class="category-text">{{ category.label }}</text>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 补贴政策列表 -->
		<scroll-view 
			scroll-y="true" 
			class="subsidy-list"
			@scrolltolower="loadMore"
			refresher-enabled="true"
			@refresherrefresh="refreshData"
			:refresher-triggered="refreshing"
		>
			<InteractiveCard 
				v-for="(item, index) in filteredSubsidies" 
				:key="item.id"
				class="subsidy-item"
				@click="viewDetail(item)"
			>
				<view class="subsidy-content">
					<!-- 补贴图标 -->
					<view class="subsidy-icon-container">
						<view class="subsidy-icon" :style="{ background: getCategoryGradient(item.category) }">
							<Icon :name="getCategoryIcon(item.category)" size="48rpx" color="#fff"></Icon>
						</view>
						<!-- 状态标识 -->
						<view class="status-badge" :class="item.status">
							<text class="status-text">{{ getStatusText(item.status) }}</text>
						</view>
					</view>

					<!-- 补贴信息 -->
					<view class="subsidy-info">
						<view class="subsidy-header">
							<text class="subsidy-title">{{ item.title }}</text>
							<view class="subsidy-amount">
								<text class="amount-text">￥{{ item.amount }}</text>
							</view>
						</view>
						
						<text class="subsidy-description">{{ item.description }}</text>
						
						<view class="subsidy-meta">
							<view class="meta-item">
								<Icon name="calendar-line" size="24rpx" color="#999"></Icon>
								<text class="meta-text">申请截止：{{ formatDate(item.deadline) }}</text>
							</view>
							<view class="meta-item">
								<Icon name="group-line" size="24rpx" color="#999"></Icon>
								<text class="meta-text">已申请：{{ item.applicants }}人</text>
							</view>
						</view>

						<view class="subsidy-conditions">
							<text class="conditions-title">申请条件：</text>
							<view class="conditions-list">
								<view 
									v-for="(condition, condIndex) in item.conditions.slice(0, 2)" 
									:key="condIndex"
									class="condition-item"
								>
									<Icon name="check-line" size="20rpx" color="#4caf50"></Icon>
									<text class="condition-text">{{ condition }}</text>
								</view>
								<text v-if="item.conditions.length > 2" class="more-conditions">
									等{{ item.conditions.length }}项条件
								</text>
							</view>
						</view>
					</view>

					<!-- 操作按钮 -->
					<view class="subsidy-actions">
						<InteractiveButton 
							v-if="item.status === 'available'"
							type="primary" 
							size="medium" 
							text="立即申请" 
							icon="file-add-line"
							@click.stop="applySubsidy(item)"
						></InteractiveButton>
						<InteractiveButton 
							v-else-if="item.status === 'applied'"
							type="secondary" 
							size="medium" 
							text="查看进度" 
							icon="eye-line"
							@click.stop="viewProgress(item)"
						></InteractiveButton>
						<InteractiveButton 
							v-else-if="item.status === 'approved'"
							type="success" 
							size="medium" 
							text="已通过" 
							icon="check-line"
							disabled
						></InteractiveButton>
						<InteractiveButton 
							v-else
							type="secondary" 
							size="medium" 
							text="了解详情" 
							icon="information-line"
							@click.stop="viewDetail(item)"
						></InteractiveButton>
					</view>
				</view>
			</InteractiveCard>

			<!-- 加载状态 -->
			<view class="load-more" v-if="hasMore && loading">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>
			<view class="no-more" v-else-if="!hasMore && filteredSubsidies.length > 0">
				<text>没有更多补贴政策了</text>
			</view>
			<view class="empty" v-else-if="!loading && filteredSubsidies.length === 0">
				<Icon name="money-dollar-circle-line" size="120rpx" color="#ccc"></Icon>
				<text class="empty-text">暂无相关补贴政策</text>
				<text class="empty-tip">请关注最新政策发布</text>
			</view>
		</scroll-view>

		<!-- 申请指南浮动按钮 -->
		<view class="guide-float" @click="showGuide">
			<Icon name="question-line" size="32rpx" color="#fff"></Icon>
			<text class="guide-text">申请指南</text>
		</view>

		<!-- 申请指南弹窗 -->
		<view v-if="showGuidePopup" class="popup-mask" @click="hideGuide">
			<view class="guide-panel" @click.stop>
				<view class="guide-header">
					<text class="guide-title">补贴申请指南</text>
					<view class="guide-close" @click="hideGuide">
						<Icon name="close-line" size="32rpx" color="#666"></Icon>
					</view>
				</view>

				<scroll-view scroll-y="true" class="guide-content">
					<view class="guide-section" v-for="(section, index) in guideSteps" :key="index">
						<view class="step-header">
							<view class="step-number">{{ index + 1 }}</view>
							<text class="step-title">{{ section.title }}</text>
						</view>
						<text class="step-description">{{ section.description }}</text>
						<view class="step-items" v-if="section.items">
							<view
								v-for="(item, itemIndex) in section.items"
								:key="itemIndex"
								class="step-item"
							>
								<Icon name="arrow-right-s-line" size="20rpx" color="#ff8a00"></Icon>
								<text class="item-text">{{ item }}</text>
							</view>
						</view>
					</view>
				</scroll-view>

				<view class="guide-actions">
					<InteractiveButton
						type="primary"
						size="large"
						text="我知道了"
						@click="hideGuide"
					></InteractiveButton>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import InteractiveCard from '@/components/InteractiveCard/InteractiveCard.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import FeedbackUtils from '@/utils/feedback.js'

export default {
	components: {
		InteractiveCard,
		InteractiveButton,
		Icon,
		PageHeader
	},
	data() {
		return {
			subsidyList: [],
			loading: false,
			refreshing: false,
			hasMore: true,
			page: 1,
			pageSize: 10,
			selectedCategory: '',
			showGuidePopup: false,
			
			// 补贴统计
			subsidyStats: [
				{ label: '可申请', value: 12, icon: 'file-add-line', color: '#4caf50' },
				{ label: '审核中', value: 3, icon: 'time-line', color: '#ff9800' },
				{ label: '已通过', value: 5, icon: 'check-line', color: '#2196f3' },
				{ label: '总金额', value: '8.5万', icon: 'money-dollar-circle-line', color: '#ff8a00' }
			],
			
			// 补贴分类
			subsidyCategories: [
				{ label: '全部', value: '', icon: 'apps-line' },
				{ label: '生活补贴', value: '生活补贴', icon: 'home-heart-line' },
				{ label: '医疗补贴', value: '医疗补贴', icon: 'health-book-line' },
				{ label: '护理补贴', value: '护理补贴', icon: 'nurse-line' },
				{ label: '住房补贴', value: '住房补贴', icon: 'building-line' },
				{ label: '交通补贴', value: '交通补贴', icon: 'bus-line' }
			],
			
			// 申请指南步骤
			guideSteps: [
				{
					title: '了解政策',
					description: '仔细阅读补贴政策的申请条件、补贴标准和申请流程',
					items: ['查看申请条件', '了解补贴金额', '确认申请时间']
				},
				{
					title: '准备材料',
					description: '根据政策要求准备相关的申请材料和证明文件',
					items: ['身份证明', '收入证明', '居住证明', '其他相关材料']
				},
				{
					title: '提交申请',
					description: '通过线上或线下方式提交申请材料',
					items: ['填写申请表', '上传材料', '提交审核']
				},
				{
					title: '跟踪进度',
					description: '关注申请审核进度，及时补充材料',
					items: ['查看审核状态', '配合调查', '等待结果']
				}
			],
			
			// 模拟补贴数据
			mockSubsidies: [
				{
					id: 1,
					title: '高龄老人生活补贴',
					category: '生活补贴',
					amount: 500,
					description: '为80岁以上高龄老人提供的生活补贴，用于改善老年人生活质量。',
					deadline: '2024-12-31',
					applicants: 1256,
					status: 'available',
					conditions: ['年满80周岁', '本市户籍', '无固定收入或收入较低']
				},
				{
					id: 2,
					title: '失能老人护理补贴',
					category: '护理补贴',
					amount: 800,
					description: '为失能、半失能老人提供的护理服务补贴，减轻家庭护理负担。',
					deadline: '2024-11-30',
					applicants: 892,
					status: 'applied',
					conditions: ['经评估为失能或半失能', '本市户籍', '家庭经济困难']
				},
				{
					id: 3,
					title: '老年人医疗救助',
					category: '医疗补贴',
					amount: 1200,
					description: '为患有重大疾病的老年人提供医疗费用救助，减轻医疗负担。',
					deadline: '2024-10-31',
					applicants: 654,
					status: 'approved',
					conditions: ['年满60周岁', '患有重大疾病', '医疗费用超过家庭承受能力']
				}
			]
		}
	},
	computed: {
		// 筛选后的补贴列表
		filteredSubsidies() {
			if (!this.selectedCategory) {
				return this.subsidyList;
			}
			return this.subsidyList.filter(item => item.category === this.selectedCategory);
		}
	},
	onLoad() {
		// 初始化离线数据
		if (typeof OfflineDataManager !== 'undefined') {
			OfflineDataManager.initOfflineData();
		}
		this.loadSubsidies();
	},
	methods: {
		// 加载补贴列表 - 100%离线模式
		async loadSubsidies() {
			try {
				this.loading = true;

				// 强制初始化离线数据
				OfflineDataManager.initOfflineData();

				// 100%使用离线数据，确保用户能看到内容
				const params = {
					page: this.page,
					pageSize: this.pageSize,
					category: this.selectedCategory
				};

				const result = OfflineDataManager.getOfflineSubsidies(params);

				if (this.page === 1) {
					this.subsidyList = result.data;
				} else {
					this.subsidyList.push(...result.data);
				}

				this.hasMore = result.data.length === this.pageSize;

				// 显示成功提示
				if (this.page === 1 && result.data.length > 0) {
					FeedbackUtils.showSuccess(`已加载 ${result.data.length} 个补贴政策`);
				}
			} catch (error) {
				console.error('加载补贴列表失败:', error);
				// 使用备用数据确保页面有内容
				this.subsidyList = [{
					id: 1,
					name: '高龄老人生活补贴',
					type: '生活补贴',
					amount: '300元/月',
					status: 'available',
					description: '为80岁以上高龄老人提供的生活补贴',
					requirements: ['年满80周岁', '本市户籍', '无固定收入来源'],
					deadline: '2024-12-31'
				}];
				FeedbackUtils.showInfo('已加载备用数据');
			} finally {
				this.loading = false;
				this.refreshing = false;
			}
		},

		// 选择分类
		selectCategory(category) {
			FeedbackUtils.lightFeedback();
			this.selectedCategory = category;
		},

		// 下拉刷新
		refreshData() {
			FeedbackUtils.lightFeedback();
			this.refreshing = true;
			this.page = 1;
			this.hasMore = true;
			this.loadSubsidies();
		},

		// 上拉加载更多
		loadMore() {
			if (this.hasMore && !this.loading) {
				this.page++;
				this.loadSubsidies();
			}
		},

		// 查看详情
		viewDetail(item) {
			FeedbackUtils.lightFeedback();
			uni.navigateTo({
				url: `/pages/subsidy/detail?id=${item.id}`
			});
		},

		// 查看申请记录
		viewApplications() {
			FeedbackUtils.lightFeedback();
			uni.navigateTo({
				url: '/pages/subsidy/applications'
			});
		},

		// 申请补贴
		applySubsidy(item) {
			FeedbackUtils.lightFeedback();
			uni.navigateTo({
				url: `/pages/subsidy/apply?id=${item.id}`
			});
		},

		// 查看进度
		viewProgress(item) {
			FeedbackUtils.lightFeedback();
			uni.navigateTo({
				url: `/pages/subsidy/progress?id=${item.id}`
			});
		},

		// 显示申请指南
		showGuide() {
			FeedbackUtils.lightFeedback();
			this.showGuidePopup = true;
		},

		// 隐藏申请指南
		hideGuide() {
			FeedbackUtils.lightFeedback();
			this.showGuidePopup = false;
		},

		// 获取分类图标
		getCategoryIcon(category) {
			const iconMap = {
				'生活补贴': 'home-heart-line',
				'医疗补贴': 'health-book-line',
				'护理补贴': 'nurse-line',
				'住房补贴': 'building-line',
				'交通补贴': 'bus-line'
			};
			return iconMap[category] || 'money-dollar-circle-line';
		},

		// 获取分类渐变色
		getCategoryGradient(category) {
			const gradientMap = {
				'生活补贴': 'linear-gradient(135deg, #4caf50 0%, #45a049 100%)',
				'医疗补贴': 'linear-gradient(135deg, #2196f3 0%, #1976d2 100%)',
				'护理补贴': 'linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%)',
				'住房补贴': 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)',
				'交通补贴': 'linear-gradient(135deg, #607d8b 0%, #455a64 100%)'
			};
			return gradientMap[category] || 'linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%)';
		},

		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'available': '可申请',
				'applied': '审核中',
				'approved': '已通过',
				'rejected': '已拒绝',
				'expired': '已过期'
			};
			return statusMap[status] || '未知';
		},

		// 格式化日期
		formatDate(dateString) {
			const date = new Date(dateString);
			const now = new Date();
			const diff = date - now;
			const days = Math.ceil(diff / (1000 * 60 * 60 * 24));

			if (days < 0) {
				return '已截止';
			} else if (days === 0) {
				return '今天截止';
			} else if (days <= 30) {
				return `${days}天后截止`;
			} else {
				return date.toLocaleDateString();
			}
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}

.page-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 40rpx;
	background: white;
	border-bottom: 1rpx solid #f0f0f0;
}

.page-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.subsidy-overview {
	padding: 30rpx 40rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	margin-bottom: 20rpx;
}

.overview-header {
	text-align: center;
	margin-bottom: 30rpx;
}

.overview-title {
	font-size: 32rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 10rpx;
}

.overview-subtitle {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
}

.stats-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}

.stat-item {
	background: rgba(255, 255, 255, 0.15);
	border-radius: 20rpx;
	padding: 25rpx;
	display: flex;
	align-items: center;
	gap: 15rpx;
	backdrop-filter: blur(10rpx);
}

.stat-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.stat-info {
	flex: 1;
}

.stat-value {
	font-size: 28rpx;
	font-weight: bold;
	color: white;
	display: block;
}

.stat-label {
	font-size: 22rpx;
	color: rgba(255, 255, 255, 0.8);
	display: block;
	margin-top: 5rpx;
}

.category-section {
	background: white;
	border-bottom: 1rpx solid #f0f0f0;
	padding: 30rpx 40rpx 20rpx 40rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.category-scroll {
	white-space: nowrap;
}

.category-list {
	display: flex;
	gap: 20rpx;
	padding-bottom: 10rpx;
}

.category-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
	padding: 20rpx 15rpx;
	border-radius: 20rpx;
	background: rgba(255, 138, 0, 0.1);
	white-space: nowrap;
	transition: all 0.3s ease;
	min-width: 100rpx;
}

.category-item.active {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
}

.category-text {
	font-size: 24rpx;
	color: #ff8a00;
}

.category-item.active .category-text {
	color: white;
	font-weight: 500;
}

.subsidy-list {
	flex: 1;
	padding: 20rpx 20rpx 40rpx;
	min-height: 0;
}

.subsidy-item {
	margin-bottom: 20rpx;
}

.subsidy-content {
	display: flex;
	padding: 30rpx;
	gap: 20rpx;
}

.subsidy-icon-container {
	position: relative;
	flex-shrink: 0;
}

.subsidy-icon {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.status-badge {
	position: absolute;
	top: -10rpx;
	right: -10rpx;
	padding: 4rpx 8rpx;
	border-radius: 10rpx;
	font-size: 18rpx;
}

.status-badge.available {
	background: #4caf50;
}

.status-badge.applied {
	background: #ff9800;
}

.status-badge.approved {
	background: #2196f3;
}

.status-badge.rejected {
	background: #f44336;
}

.status-text {
	color: white;
	font-size: 18rpx;
}

.subsidy-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.subsidy-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	gap: 15rpx;
}

.subsidy-title {
	flex: 1;
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	line-height: 1.3;
}

.subsidy-amount {
	flex-shrink: 0;
}

.amount-text {
	font-size: 28rpx;
	font-weight: bold;
	color: #ff8a00;
}

.subsidy-description {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.subsidy-meta {
	display: flex;
	gap: 20rpx;
}

.meta-item {
	display: flex;
	align-items: center;
	gap: 6rpx;
}

.meta-text {
	font-size: 22rpx;
	color: #999;
}

.subsidy-conditions {
	margin-top: 10rpx;
}

.conditions-title {
	font-size: 24rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 8rpx;
}

.conditions-list {
	display: flex;
	flex-direction: column;
	gap: 6rpx;
}

.condition-item {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.condition-text {
	font-size: 22rpx;
	color: #666;
}

.more-conditions {
	font-size: 22rpx;
	color: #999;
	margin-left: 28rpx;
}

.subsidy-actions {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: flex-end;
}

.guide-float {
	position: fixed;
	bottom: 40rpx;
	right: 40rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	border-radius: 30rpx;
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
	gap: 10rpx;
	box-shadow: 0 4rpx 20rpx rgba(255, 138, 0, 0.3);
	z-index: 100;
}

.guide-text {
	font-size: 26rpx;
	color: white;
	font-weight: 500;
}

.load-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
	gap: 20rpx;
}

.loading-spinner {
	width: 40rpx;
	height: 40rpx;
	border: 3rpx solid rgba(255, 138, 0, 0.2);
	border-top: 3rpx solid #ff8a00;
	border-radius: 50%;
	animation: loading 1s linear infinite;
}

.loading-text {
	font-size: 26rpx;
	color: #999;
}

.no-more {
	text-align: center;
	padding: 40rpx;
	color: #999;
	font-size: 26rpx;
}

.empty {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 40rpx;
	gap: 20rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

.empty-tip {
	font-size: 24rpx;
	color: #ccc;
}

/* 申请指南面板 */
.guide-panel {
	background: white;
	border-radius: 30rpx 30rpx 0 0;
	max-height: 80vh;
	overflow: hidden;
}

.guide-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.guide-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.guide-close {
	padding: 10rpx;
}

.guide-content {
	max-height: 50vh;
	padding: 30rpx;
}

.guide-section {
	margin-bottom: 40rpx;
}

.step-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 15rpx;
}

.step-number {
	width: 40rpx;
	height: 40rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	color: white;
	font-size: 24rpx;
	font-weight: bold;
}

.step-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.step-description {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	margin-bottom: 15rpx;
}

.step-items {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.step-item {
	display: flex;
	align-items: center;
	gap: 10rpx;
	padding-left: 20rpx;
}

.item-text {
	font-size: 24rpx;
	color: #666;
}

.guide-actions {
	padding: 30rpx;
	border-top: 1rpx solid #f0f0f0;
}

/* 弹窗样式 */
.popup-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: flex-end;
	justify-content: center;
	z-index: 9999;
}

.guide-panel {
	background: white;
	border-radius: 20rpx 20rpx 0 0;
	width: 100%;
	max-height: 80vh;
	display: flex;
	flex-direction: column;
	animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
	from {
		transform: translateY(100%);
	}
	to {
		transform: translateY(0);
	}
}

@keyframes loading {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}
</style>
