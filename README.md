# 智慧养老 uni-app 项目

这是一个基于 uni-app 框架开发的智慧养老应用，旨在为老年人提供便捷的养老服务。

## 项目特色

- 🎯 **适老化设计** - 专为老年人设计的友好界面
- 📱 **跨平台支持** - 支持微信小程序、H5、App等多端运行
- 🔧 **组件化开发** - 完整的组件库，易于维护和扩展
- 💾 **离线数据** - 支持离线数据存储，无网络也能使用基本功能
- 🎨 **现代UI** - 采用现代化的UI设计，交互流畅

## 项目结构

```
智慧养老/
├── components/              # 全局组件库
│   ├── Icon/               # 图标组件
│   ├── LoadingSkeleton/    # 骨架屏组件
│   ├── ErrorBoundary/      # 错误边界组件
│   ├── LazyImage/          # 懒加载图片组件
│   ├── InteractiveCard/    # 交互卡片组件
│   ├── InteractiveButton/  # 交互按钮组件
│   ├── FormBuilder/        # 表单构建器组件
│   └── PageHeader/         # 页面头部组件
├── pages/                  # 页面文件
│   ├── index/              # 启动页
│   ├── home/               # 首页
│   ├── login/              # 登录页
│   ├── workspace/          # 工作台
│   ├── map/                # 地图页
│   ├── profile/            # 个人中心
│   ├── institution/        # 机构相关页面
│   ├── service/            # 服务相关页面
│   ├── subsidy/            # 补贴相关页面
│   ├── news/               # 资讯相关页面
│   └── test/               # 测试页面
├── utils/                  # 工具类
│   ├── feedback.js         # 反馈工具类
│   ├── offlineData.js      # 离线数据管理
│   └── mockData.js         # 模拟数据工具
├── static/                 # 静态资源
├── App.vue                 # 应用入口
├── main.js                 # 主入口文件
├── pages.json              # 页面配置
├── manifest.json           # 应用配置
└── uni.scss                # 全局样式
```

## 核心功能

### 🏠 首页
- 轮播图展示
- 快捷服务入口
- 推荐机构展示
- 最新资讯

### 🏢 机构服务
- 养老机构查找
- 机构详情查看
- 在线预约
- 评价反馈

### 💰 补贴申请
- 高龄津贴申请
- 养老服务补贴
- 适老化改造补贴
- 申请进度查询

### 🗺️ 地图导航
- 附近机构查找
- 路线导航
- 实时位置
- 分类筛选

### 👤 个人中心
- 个人信息管理
- 订单记录
- 收藏管理
- 设置中心

## 组件库说明

### Icon 图标组件
基于 emoji 的图标系统，支持多种尺寸和颜色。

```vue
<Icon name="home-line" size="32rpx" color="#ff8a00" />
```

### InteractiveButton 交互按钮
支持多种类型、尺寸和状态的按钮组件。

```vue
<InteractiveButton 
  type="primary" 
  text="确定"
  :loading="loading"
  @click="handleClick"
/>
```

### InteractiveCard 交互卡片
可点击的卡片组件，支持加载状态和阴影效果。

```vue
<InteractiveCard :elevated="true" @click="handleClick">
  <view>卡片内容</view>
</InteractiveCard>
```

### LoadingSkeleton 骨架屏
多种类型的骨架屏组件，提升用户体验。

```vue
<LoadingSkeleton type="list" :count="3" />
```

### FormBuilder 表单构建器
动态表单生成器，支持多种表单控件。

```vue
<FormBuilder :fields="formFields" v-model="formData" />
```

## 工具类说明

### FeedbackUtils 反馈工具
统一的用户反馈方法，包括提示、确认、加载等。

```javascript
import FeedbackUtils from '@/utils/feedback.js'

// 显示成功提示
FeedbackUtils.showSuccess('操作成功')

// 显示确认对话框
const confirmed = await FeedbackUtils.showConfirm('确定删除吗？')
```

### OfflineDataManager 离线数据管理
提供离线数据存储和获取功能。

```javascript
import OfflineDataManager from '@/utils/offlineData.js'

// 获取机构列表
const institutions = OfflineDataManager.getOfflineInstitutions()
```

### MockAPI 模拟数据
提供模拟的API接口数据，用于开发和测试。

```javascript
import MockAPI from '@/utils/mockData.js'

// 获取轮播图数据
const banners = await MockAPI.getBannerList()
```

## 开发指南

### 环境要求
- HBuilderX 3.0+
- Node.js 14+
- 微信开发者工具（小程序开发）

### 安装依赖
```bash
npm install
```

### 运行项目
1. 使用 HBuilderX 打开项目
2. 选择运行平台（微信小程序/H5/App）
3. 点击运行

### 编译打包
1. 在 HBuilderX 中选择发行
2. 选择目标平台
3. 配置相关参数
4. 点击打包

## 适老化特性

### 大字体支持
项目支持适老版模式，提供更大的字体和按钮。

```vue
<view class="elderly-mode">
  <!-- 适老版内容 -->
</view>
```

### 简化操作
- 减少操作步骤
- 明确的视觉反馈
- 语音提示支持

### 无障碍访问
- 高对比度色彩
- 清晰的图标设计
- 简洁的界面布局

## 数据管理

### 本地存储
使用 uni-app 的存储API进行数据持久化：

```javascript
// 存储数据
uni.setStorageSync('key', value)

// 获取数据
const value = uni.getStorageSync('key')
```

### 离线支持
项目内置离线数据管理，确保在无网络环境下也能正常使用基本功能。

## 测试

### 组件测试
访问 `/pages/test/icons` 页面可以测试所有组件的功能。

### 功能测试
- 页面跳转测试
- 数据加载测试
- 交互功能测试
- 适老版模式测试

## 部署说明

### 微信小程序
1. 在微信开发者工具中导入项目
2. 配置 AppID
3. 上传代码审核

### H5部署
1. 运行到浏览器生成 H5 版本
2. 将 dist 目录部署到服务器
3. 配置域名和HTTPS

### App打包
1. 配置原生插件
2. 生成离线打包资源
3. 使用云打包或本地打包

## 注意事项

1. **图标资源**: 当前使用 emoji 作为图标，生产环境建议替换为真实图标文件
2. **API接口**: 当前使用模拟数据，需要替换为真实的后端接口
3. **地图功能**: 需要申请相应的地图服务密钥
4. **权限配置**: 根据实际需求配置相应的系统权限

## 技术支持

如有问题，请查看：
- [uni-app 官方文档](https://uniapp.dcloud.io/)
- [HBuilderX 使用指南](https://hx.dcloud.io/)
- 项目内的测试页面和示例代码

## 更新日志

### v1.0.0
- 初始版本发布
- 完整的组件库
- 基础功能实现
- 适老化设计支持
