<template>
	<!-- SVG图标 -->
	<image
		v-if="iconType === 'svg'"
		:src="iconSrc"
		class="icon icon-svg"
		:class="[
			{ 'icon-primary': primary },
			{ 'icon-secondary': secondary },
			{ 'icon-institution': institution },
			{ 'icon-service': service },
			{ 'icon-elderly': elderly },
			customClass
		]"
		:style="iconStyle"
		@click="handleClick"
		mode="aspectFit"
	/>

	<!-- 图片图标 -->
	<image
		v-else-if="iconType === 'image'"
		:src="iconSrc"
		class="icon icon-image"
		:class="[
			{ 'icon-primary': primary },
			{ 'icon-secondary': secondary },
			{ 'icon-institution': institution },
			{ 'icon-service': service },
			{ 'icon-elderly': elderly },
			customClass
		]"
		:style="iconStyle"
		@click="handleClick"
		mode="aspectFit"
	/>

	<!-- Emoji图标 -->
	<text
		v-else
		class="icon icon-emoji"
		:class="[
			{ 'icon-primary': primary },
			{ 'icon-secondary': secondary },
			{ 'icon-institution': institution },
			{ 'icon-service': service },
			{ 'icon-elderly': elderly },
			customClass
		]"
		:style="iconStyle"
		@click="handleClick"
	>
		{{ iconText }}
	</text>
</template>

<script>
import { getIconEmoji, hasIcon, getIconConfig } from '@/utils/iconConfig.js'
import { getCompatibleIconName } from '@/utils/iconCompatibility.js'

export default {
	name: 'Icon',
	props: {
		name: {
			type: String,
			required: true
		},
		size: {
			type: String,
			default: '32rpx'
		},
		color: {
			type: String,
			default: ''
		},
		// 主题色快捷方式
		primary: {
			type: Boolean,
			default: false
		},
		secondary: {
			type: Boolean,
			default: false
		},
		institution: {
			type: Boolean,
			default: false
		},
		service: {
			type: Boolean,
			default: false
		},
		elderly: {
			type: Boolean,
			default: false
		},
		customClass: {
			type: String,
			default: ''
		},
		// 图标类型：auto(自动检测)、emoji、svg、image
		type: {
			type: String,
			default: 'auto',
			validator: value => ['auto', 'emoji', 'svg', 'image'].includes(value)
		},
		// 自定义图标路径（当type为svg或image时使用）
		src: {
			type: String,
			default: ''
		}
	},
	computed: {
		// 兼容的图标名称
		compatibleIconName() {
			return getCompatibleIconName(this.name)
		},

		// 图标类型判断
		iconType() {
			// 如果指定了类型，直接使用
			if (this.type !== 'auto') {
				return this.type
			}

			// 如果提供了src，判断是SVG还是图片
			if (this.src) {
				return this.src.toLowerCase().endsWith('.svg') ? 'svg' : 'image'
			}

			// 检查是否有对应的SVG文件
			const svgPath = `/static/icons/${this.compatibleIconName}.svg`
			// 注意：在实际使用中，这里可能需要异步检查文件是否存在
			// 暂时默认为emoji类型
			return 'emoji'
		},

		// 图标源路径
		iconSrc() {
			if (this.src) {
				return this.src
			}

			// 检查配置文件中是否有SVG路径（使用兼容的图标名称）
			if (hasIcon(this.compatibleIconName)) {
				const config = getIconConfig(this.compatibleIconName)
				if (config.svg) {
					return config.svg
				}
			}

			if (this.iconType === 'svg') {
				return `/static/icons/${this.compatibleIconName}.svg`
			}

			if (this.iconType === 'image') {
				return `/static/icons/${this.compatibleIconName}.png`
			}

			return ''
		},

		// Emoji图标文本
		iconText() {
			// 优先使用配置文件中的图标（使用兼容的图标名称）
			if (hasIcon(this.compatibleIconName)) {
				return getIconEmoji(this.compatibleIconName)
			}

			// 兼容旧的图标映射（逐步迁移）
			const legacyIconMap = {
				// 导航图标 - 统一使用location-line替代map-pin-line
				'map-pin-line': '📍', // 建议使用location-line
				'notification-3-line': '🔔',
				'dashboard-line': '📊',

				// 紧急服务图标
				'parent-line': '👨‍👩‍👧‍👦',

				// 登录页面图标
				'lock-password-line': '🔒',
				'wechat-line': '💬',
				'user-smile-line': '😊',
				'fingerprint-line': '👆',

				// 扩展图标（未在配置文件中的）
				'filter-line': '🔽',
				'sort-line': '📶',
				'question-line': '❓',
				'success-line': '✅',
				'video-line': '🎥',
				'music-line': '🎵',
				'wifi-line': '📶',
				'bluetooth-line': '📶',
				'signal-line': '📶',
				'car-line': '🚗',
				'bus-line': '🚌',
				'train-line': '🚆',
				'plane-line': '✈️',
				'ship-line': '🚢',
				'bike-line': '🚲',
				'walk-line': '🚶',
				'bookmark-line': '🔖',
				'flag-line': '🚩',
				'shopping-cart-line': '🛒',
				'wallet-line': '👛',
				'bank-card-line': '💳',
				'coin-line': '🪙',
				'medicine-bottle-line': '💊',
				'stethoscope-line': '🩺',
				'thermometer-line': '🌡️',
				'syringe-line': '💉',
				'cake-line': '🍰',
				'coffee-line': '☕',
				'bowl-line': '🍜',
				'apple-line': '🍎',
				'sun-line': '☀️',
				'moon-line': '🌙',
				'cloud-line': '☁️',
				'rain-line': '🌧️',
				'snow-line': '❄️'
			}

			return legacyIconMap[this.name] || '⚙️' // 使用设置图标代替问号，表示需要配置
		},

		// 图标样式
		iconStyle() {
			const style = {}

			// 设置尺寸
			if (this.iconType === 'emoji') {
				style.fontSize = this.size
			} else {
				style.width = this.size
				style.height = this.size
			}

			// 设置颜色（仅对emoji有效）
			if (this.color && this.iconType === 'emoji') {
				style.color = this.color
			}

			return style
		}
	},
	methods: {
		handleClick(e) {
			this.$emit('click', e)
		}
	}
}
</script>

<style scoped>
/* 基础图标样式 */
.icon {
	display: inline-block;
	text-align: center;
	line-height: 1;
	vertical-align: middle;
	user-select: none;
	cursor: pointer;
	transition: all 0.3s ease;
}

/* Emoji图标样式 */
.icon-emoji {
	font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
}

/* SVG和图片图标样式 */
.icon-svg,
.icon-image {
	object-fit: contain;
}

/* 主题色样式 */
.icon-primary {
	color: #ff8a00;
}

.icon-secondary {
	color: #666666;
}

.icon-institution {
	color: #ff6b6b;
}

.icon-service {
	color: #4ecdc4;
}

.icon-elderly {
	color: #96ceb4;
}

/* 悬停效果 */
.icon:hover {
	transform: scale(1.1);
	opacity: 0.8;
}

/* 激活效果 */
.icon:active {
	transform: scale(0.95);
}

/* 禁用状态 */
.icon.disabled {
	opacity: 0.5;
	cursor: not-allowed;
	pointer-events: none;
}

/* 加载状态 */
.icon.loading {
	animation: icon-spin 1s linear infinite;
}

@keyframes icon-spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

/* 响应式尺寸 */
@media (max-width: 750rpx) {
	.icon {
		transform-origin: center;
	}
}
</style>
