/**
 * 模拟API数据工具
 * 提供模拟的API接口数据，用于开发和测试
 */

class MockAPI {
	/**
	 * 模拟网络延迟
	 * @param {number} delay 延迟时间（毫秒）
	 */
	static async delay(delay = 500) {
		return new Promise(resolve => setTimeout(resolve, delay))
	}

	/**
	 * 模拟API响应格式
	 * @param {*} data 响应数据
	 * @param {boolean} success 是否成功
	 * @param {string} message 响应消息
	 */
	static createResponse(data = null, success = true, message = 'success') {
		return {
			code: success ? 200 : 500,
			success,
			message,
			data,
			timestamp: Date.now()
		}
	}

	/**
	 * 获取首页轮播图数据
	 */
	static async getBannerList() {
		await this.delay(300)
		
		const banners = [
			{
				id: 1,
				title: '智慧养老新体验',
				image: '/picture/nursing_home_1.jpg',
				link: '/pages/news/detail?id=1',
				type: 'news'
			},
			{
				id: 2,
				title: '养老服务全覆盖',
				image: '/picture/nursing_home_2.jpg',
				link: '/pages/service/list',
				type: 'service'
			},
			{
				id: 3,
				title: '补贴政策新发布',
				image: '/picture/nursing_home_3.jpg',
				link: '/pages/subsidy/list',
				type: 'subsidy'
			}
		]

		return this.createResponse(banners)
	}

	/**
	 * 获取推荐机构列表
	 */
	static async getRecommendInstitutions() {
		await this.delay(400)
		
		const institutions = [
			{
				id: 1,
				name: '阳光养老院',
				rating: 4.8,
				price: '3000-5000元/月',
				distance: '1.2km',
				image: '/static/institutions/institution1.jpg',
				tags: ['医护齐全', '环境优美', '交通便利']
			},
			{
				id: 2,
				name: '康乐老年公寓',
				rating: 4.6,
				price: '2500-4000元/月',
				distance: '2.1km',
				image: '/static/institutions/institution2.jpg',
				tags: ['温馨舒适', '活动丰富', '价格实惠']
			},
			{
				id: 3,
				name: '温馨护理中心',
				rating: 4.9,
				price: '4000-6000元/月',
				distance: '3.5km',
				image: '/static/institutions/institution3.jpg',
				tags: ['专业护理', '设备先进', '服务贴心']
			}
		]

		return this.createResponse(institutions)
	}

	/**
	 * 获取任务列表
	 */
	static async getTaskList(params = {}) {
		await this.delay(500)
		
		const { page = 1, pageSize = 10, status = 'all' } = params
		
		const allTasks = [
			{
				id: 1,
				title: '完善个人信息',
				description: '请完善您的个人基本信息，以便为您提供更好的服务',
				status: 'pending',
				priority: 'high',
				deadline: '2024-02-01',
				progress: 60,
				category: 'profile',
				createTime: '2024-01-10'
			},
			{
				id: 2,
				title: '健康体检预约',
				description: '建议您定期进行健康体检，及时了解身体状况',
				status: 'pending',
				priority: 'medium',
				deadline: '2024-02-15',
				progress: 0,
				category: 'health',
				createTime: '2024-01-12'
			},
			{
				id: 3,
				title: '申请高龄津贴',
				description: '您符合高龄津贴申请条件，请及时申请',
				status: 'completed',
				priority: 'high',
				deadline: '2024-01-20',
				progress: 100,
				category: 'subsidy',
				createTime: '2024-01-05',
				completeTime: '2024-01-18'
			},
			{
				id: 4,
				title: '紧急联系人设置',
				description: '设置紧急联系人，确保紧急情况下能及时联系',
				status: 'pending',
				priority: 'high',
				deadline: '2024-01-25',
				progress: 30,
				category: 'safety',
				createTime: '2024-01-08'
			},
			{
				id: 5,
				title: '参加社区活动',
				description: '社区将举办老年人文艺活动，欢迎您参加',
				status: 'pending',
				priority: 'low',
				deadline: '2024-02-10',
				progress: 0,
				category: 'activity',
				createTime: '2024-01-15'
			}
		]

		// 状态筛选
		let filteredTasks = allTasks
		if (status !== 'all') {
			filteredTasks = allTasks.filter(task => task.status === status)
		}

		// 分页
		const startIndex = (page - 1) * pageSize
		const endIndex = startIndex + pageSize
		const tasks = filteredTasks.slice(startIndex, endIndex)

		return this.createResponse({
			list: tasks,
			total: filteredTasks.length,
			page,
			pageSize,
			hasMore: endIndex < filteredTasks.length
		})
	}

	/**
	 * 获取通知列表
	 */
	static async getNotificationList(params = {}) {
		await this.delay(400)
		
		const { page = 1, pageSize = 10 } = params
		
		const notifications = [
			{
				id: 1,
				title: '新政策发布通知',
				content: '关于调整养老服务补贴标准的通知已发布，请及时查看。',
				type: 'policy',
				isRead: false,
				createTime: '2024-01-16 10:30',
				importance: 'high'
			},
			{
				id: 2,
				title: '服务预约提醒',
				content: '您预约的居家护理服务将于明天上午9点开始，请做好准备。',
				type: 'service',
				isRead: false,
				createTime: '2024-01-15 16:20',
				importance: 'medium'
			},
			{
				id: 3,
				title: '健康体检通知',
				content: '社区健康体检活动即将开始，请携带相关证件参加。',
				type: 'health',
				isRead: true,
				createTime: '2024-01-14 09:15',
				importance: 'medium'
			},
			{
				id: 4,
				title: '系统维护通知',
				content: '系统将于今晚22:00-24:00进行维护，期间可能影响部分功能使用。',
				type: 'system',
				isRead: true,
				createTime: '2024-01-13 14:45',
				importance: 'low'
			}
		]

		// 分页
		const startIndex = (page - 1) * pageSize
		const endIndex = startIndex + pageSize
		const list = notifications.slice(startIndex, endIndex)

		return this.createResponse({
			list,
			total: notifications.length,
			page,
			pageSize,
			hasMore: endIndex < notifications.length,
			unreadCount: notifications.filter(n => !n.isRead).length
		})
	}

	/**
	 * 获取用户统计数据
	 */
	static async getUserStats() {
		await this.delay(300)
		
		const stats = {
			totalTasks: 12,
			completedTasks: 8,
			pendingTasks: 4,
			totalServices: 5,
			activeServices: 2,
			totalSubsidies: 3,
			approvedSubsidies: 2,
			unreadNotifications: 3,
			healthScore: 85,
			lastCheckTime: '2024-01-10',
			nextAppointment: '2024-01-20 14:00'
		}

		return this.createResponse(stats)
	}

	/**
	 * 获取地图数据
	 */
	static async getMapData(params = {}) {
		await this.delay(600)
		
		const { type = 'all', latitude, longitude } = params
		
		const mapData = [
			{
				id: 1,
				name: '阳光养老院',
				type: 'institution',
				latitude: 39.9042,
				longitude: 116.4074,
				address: '北京市朝阳区阳光街123号',
				phone: '010-12345678',
				rating: 4.8,
				distance: 1200
			},
			{
				id: 2,
				name: '社区卫生服务中心',
				type: 'hospital',
				latitude: 39.9142,
				longitude: 116.4174,
				address: '北京市朝阳区健康路456号',
				phone: '010-87654321',
				rating: 4.5,
				distance: 800
			},
			{
				id: 3,
				name: '老年活动中心',
				type: 'activity',
				latitude: 39.8942,
				longitude: 116.3974,
				address: '北京市朝阳区文化路789号',
				phone: '010-11223344',
				rating: 4.7,
				distance: 1500
			}
		]

		// 类型筛选
		let filteredData = mapData
		if (type !== 'all') {
			filteredData = mapData.filter(item => item.type === type)
		}

		return this.createResponse(filteredData)
	}

	/**
	 * 模拟登录
	 */
	static async login(username, password) {
		await this.delay(1000)
		
		// 简单的模拟验证
		if (username && password) {
			const userInfo = {
				id: 1,
				username,
				name: '张大爷',
				phone: '138****5678',
				avatar: '/static/avatar/default.png',
				token: 'mock_token_' + Date.now(),
				loginTime: new Date().toISOString()
			}
			
			return this.createResponse(userInfo, true, '登录成功')
		} else {
			return this.createResponse(null, false, '用户名或密码不能为空')
		}
	}

	/**
	 * 模拟发送验证码
	 */
	static async sendSmsCode(phone) {
		await this.delay(800)
		
		if (phone && phone.length === 11) {
			return this.createResponse({ code: '123456' }, true, '验证码发送成功')
		} else {
			return this.createResponse(null, false, '手机号格式不正确')
		}
	}

	/**
	 * 模拟数据提交
	 */
	static async submitData(data) {
		await this.delay(1200)

		// 模拟随机成功/失败
		const success = Math.random() > 0.1 // 90%成功率

		if (success) {
			return this.createResponse({ id: Date.now() }, true, '提交成功')
		} else {
			return this.createResponse(null, false, '提交失败，请重试')
		}
	}

	/**
	 * 导出所有数据
	 */
	static async exportData() {
		await this.delay(1500)

		try {
			const allData = {
				institutions: LocalStorage.get('institutions', []),
				services: LocalStorage.get('services', []),
				favorites: LocalStorage.get('favorites', []),
				healthData: LocalStorage.get('healthData', []),
				tasks: LocalStorage.get('tasks', []),
				userInfo: LocalStorage.get('userInfo', {}),
				settings: LocalStorage.get('settings', {}),
				exportTime: new Date().toISOString(),
				version: '1.0.0'
			}

			const jsonData = JSON.stringify(allData, null, 2)
			return this.createResponse(jsonData, true, '数据导出成功')
		} catch (error) {
			return this.createResponse(null, false, '数据导出失败：' + error.message)
		}
	}

	/**
	 * 导入数据
	 */
	static async importData(jsonData) {
		await this.delay(2000)

		try {
			const data = JSON.parse(jsonData)

			// 验证数据格式
			if (!data || typeof data !== 'object') {
				return this.createResponse(null, false, '数据格式不正确')
			}

			// 导入各类数据
			if (data.institutions) LocalStorage.set('institutions', data.institutions)
			if (data.services) LocalStorage.set('services', data.services)
			if (data.favorites) LocalStorage.set('favorites', data.favorites)
			if (data.healthData) LocalStorage.set('healthData', data.healthData)
			if (data.tasks) LocalStorage.set('tasks', data.tasks)
			if (data.userInfo) LocalStorage.set('userInfo', data.userInfo)
			if (data.settings) LocalStorage.set('settings', data.settings)

			return this.createResponse(null, true, '数据导入成功')
		} catch (error) {
			return this.createResponse(null, false, '数据导入失败：数据格式错误')
		}
	}

	/**
	 * 清空所有数据
	 */
	static async clearAllData() {
		await this.delay(1000)

		try {
			// 清空所有存储的数据
			const keysToRemove = [
				'institutions',
				'services',
				'favorites',
				'healthData',
				'tasks',
				'userInfo',
				'settings',
				'notificationSettings',
				'displaySettings',
				'languageSettings',
				'emergencyContacts',
				'contactInfo'
			]

			keysToRemove.forEach(key => {
				LocalStorage.remove(key)
			})

			return this.createResponse(null, true, '数据清空成功')
		} catch (error) {
			return this.createResponse(null, false, '数据清空失败：' + error.message)
		}
	}
}

/**
 * 本地存储工具类
 */
class LocalStorage {
	/**
	 * 获取存储数据
	 */
	static get(key, defaultValue = null) {
		try {
			const value = uni.getStorageSync(key)
			return value !== '' ? value : defaultValue
		} catch (error) {
			console.error('获取存储数据失败:', error)
			return defaultValue
		}
	}

	/**
	 * 设置存储数据
	 */
	static set(key, value) {
		try {
			uni.setStorageSync(key, value)
			return true
		} catch (error) {
			console.error('设置存储数据失败:', error)
			return false
		}
	}

	/**
	 * 删除存储数据
	 */
	static remove(key) {
		try {
			uni.removeStorageSync(key)
			return true
		} catch (error) {
			console.error('删除存储数据失败:', error)
			return false
		}
	}

	/**
	 * 清空所有存储数据
	 */
	static clear() {
		try {
			uni.clearStorageSync()
			return true
		} catch (error) {
			console.error('清空存储数据失败:', error)
			return false
		}
	}

	/**
	 * 获取存储信息
	 */
	static getInfo() {
		try {
			return uni.getStorageInfoSync()
		} catch (error) {
			console.error('获取存储信息失败:', error)
			return { keys: [], currentSize: 0, limitSize: 0 }
		}
	}
}

export default MockAPI
export { LocalStorage }
