<template>
	<button 
		class="interactive-button" 
		:class="[
			`btn-${type}`,
			`btn-${size}`,
			{ 'btn-loading': loading },
			{ 'btn-disabled': disabled },
			{ 'btn-block': block },
			{ 'btn-round': round },
			{ 'elderly-mode': elderlyMode },
			customClass
		]"
		:style="buttonStyle"
		:disabled="disabled || loading"
		@click="handleClick"
		@touchstart="handleTouchStart"
		@touchend="handleTouchEnd"
	>
		<!-- 加载状态 -->
		<view v-if="loading" class="btn-loading-content">
			<view class="loading-spinner"></view>
			<text v-if="loadingText" class="btn-text-content">{{ loadingText }}</text>
		</view>

		<!-- 正常状态 -->
		<view v-else class="btn-content">
			<!-- 左侧图标 -->
			<Icon
				v-if="leftIcon"
				:name="leftIcon"
				:size="iconSize"
				:color="iconColor"
				class="btn-icon left"
			/>

			<!-- 按钮文字 -->
			<text class="btn-text-content">
				<slot>{{ text }}</slot>
			</text>

			<!-- 右侧图标 -->
			<Icon
				v-if="rightIcon"
				:name="rightIcon"
				:size="iconSize"
				:color="iconColor"
				class="btn-icon right"
			/>
		</view>
	</button>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	name: 'InteractiveButton',
	components: {
		Icon
	},
	props: {
		type: {
			type: String,
			default: 'primary',
			validator: value => ['primary', 'secondary', 'success', 'warning', 'danger', 'info', 'text'].includes(value)
		},
		size: {
			type: String,
			default: 'medium',
			validator: value => ['small', 'medium', 'large'].includes(value)
		},
		text: {
			type: String,
			default: ''
		},
		loading: {
			type: Boolean,
			default: false
		},
		loadingText: {
			type: String,
			default: '加载中...'
		},
		disabled: {
			type: Boolean,
			default: false
		},
		block: {
			type: Boolean,
			default: false
		},
		round: {
			type: Boolean,
			default: false
		},
		elderlyMode: {
			type: Boolean,
			default: false
		},
		leftIcon: {
			type: String,
			default: ''
		},
		rightIcon: {
			type: String,
			default: ''
		},
		customClass: {
			type: String,
			default: ''
		},
		width: {
			type: [String, Number],
			default: ''
		},
		height: {
			type: [String, Number],
			default: ''
		}
	},
	computed: {
		buttonStyle() {
			const style = {}
			
			if (this.width) {
				style.width = typeof this.width === 'number' ? this.width + 'rpx' : this.width
			}
			
			if (this.height) {
				style.height = typeof this.height === 'number' ? this.height + 'rpx' : this.height
			}
			
			return style
		},
		iconSize() {
			const sizeMap = {
				small: '24rpx',
				medium: '28rpx',
				large: '32rpx'
			}
			return this.elderlyMode ? '32rpx' : sizeMap[this.size]
		},
		iconColor() {
			if (this.type === 'text') {
				return '#ff8a00'
			}
			return this.type === 'secondary' ? '#666' : '#fff'
		}
	},
	methods: {
		handleClick(e) {
			if (this.disabled || this.loading) {
				return
			}
			
			// 添加点击反馈
			this.addClickFeedback()
			
			this.$emit('click', e)
		},
		handleTouchStart() {
			if (this.disabled || this.loading) {
				return
			}
		},
		handleTouchEnd() {
			// 触摸结束处理
		},
		addClickFeedback() {
			// 添加轻微的震动反馈
			try {
				uni.vibrateShort({
					type: 'light'
				})
			} catch (e) {
				// 忽略不支持震动的设备
			}
		}
	}
}
</script>

<style scoped>
/* ================================
   iOS风格InteractiveButton组件
   基于iOS Human Interface Guidelines
   ================================ */

.interactive-button {
	border: none;
	border-radius: 16rpx; /* iOS标准圆角 */
	font-size: 34rpx; /* iOS Body字体大小 */
	font-weight: 600; /* iOS Semibold字重 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Helvetica, Arial, sans-serif;
	text-align: center;
	cursor: pointer;
	transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94); /* iOS标准缓动 */
	position: relative;
	overflow: hidden;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;
	box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.06); /* iOS风格阴影 */
	-webkit-tap-highlight-color: transparent; /* 移除iOS点击高亮 */
	user-select: none;
	-webkit-user-select: none;
}

.interactive-button::after {
	border: none;
}

/* iOS风格按钮类型样式 */
.btn-primary {
	background: #ff8a00; /* 移除渐变，使用纯色 */
	color: white;
}

.btn-primary:active {
	background: #f57c00; /* 按压时的深色 */
}

.btn-secondary {
	background: #F3F4F6; /* iOS风格的次要背景色 */
	color: #374151; /* iOS风格的文字色 */
	border: 1rpx solid #E5E7EB; /* iOS风格的边框 */
}

.btn-secondary:active {
	background: #E5E7EB;
	color: #1F2937;
}

.btn-success {
	background: #34C759; /* iOS绿色 */
	color: white;
}

.btn-success:active {
	background: #28A745;
}

.btn-warning {
	background: #FF9500; /* iOS橙色 */
	color: white;
}

.btn-warning:active {
	background: #E8890B;
}

.btn-danger {
	background: #FF3B30; /* iOS红色 */
	color: white;
}

.btn-danger:active {
	background: #E02D20;
}

.btn-info {
	background: #007AFF; /* iOS蓝色 */
	color: white;
}

.btn-info:active {
	background: #0056CC;
}

/* 文本类型按钮样式 */
.btn-text {
	background: transparent;
	color: #ff8a00;
	border: none;
	box-shadow: none;
}

.btn-text:active {
	background: rgba(255, 138, 0, 0.1);
}

/* iOS风格按钮尺寸 */
.btn-small {
	padding: 12rpx 24rpx; /* iOS小按钮内边距 */
	font-size: 26rpx; /* iOS Footnote字体 */
	border-radius: 12rpx; /* iOS小圆角 */
	min-height: 64rpx; /* iOS最小触摸目标 */
}

.btn-medium {
	padding: 16rpx 32rpx; /* iOS中等按钮内边距 */
	font-size: 34rpx; /* iOS Body字体 */
	border-radius: 16rpx; /* iOS中等圆角 */
	min-height: 88rpx; /* iOS标准触摸目标 */
}

.btn-large {
	padding: 20rpx 40rpx; /* iOS大按钮内边距 */
	font-size: 36rpx; /* iOS Headline字体 */
	border-radius: 20rpx; /* iOS大圆角 */
	min-height: 112rpx; /* iOS大触摸目标 */
}

/* iOS风格按钮状态 */
.btn-loading {
	pointer-events: none;
	opacity: 0.6; /* iOS风格的加载透明度 */
}

.btn-disabled {
	pointer-events: none;
	opacity: 0.4; /* iOS风格的禁用透明度 */
	background: #E5E7EB !important; /* iOS风格的禁用背景 */
	color: #9CA3AF !important; /* iOS风格的禁用文字 */
	box-shadow: none !important;
}

.btn-disabled .btn-text-content {
	color: #9CA3AF !important; /* 确保禁用状态下文字颜色正确 */
}

.btn-block {
	width: 100%;
	display: flex;
}

.btn-round {
	border-radius: 50rpx; /* 完全圆角 */
}

/* iOS风格按钮内容 */
.btn-content,
.btn-loading-content {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8rpx; /* iOS标准间距 */
}

/* 按钮内文字样式 */
.btn-text-content {
	line-height: 1.2; /* iOS标准行高 */
	letter-spacing: -0.01em; /* iOS字母间距 */
	color: inherit; /* 继承按钮的字体颜色 */
}

/* 确保不同按钮类型的文字颜色正确 */
.btn-primary .btn-text-content,
.btn-success .btn-text-content,
.btn-warning .btn-text-content,
.btn-danger .btn-text-content,
.btn-info .btn-text-content {
	color: white; /* 深色背景按钮使用白色文字 */
}

.btn-secondary .btn-text-content {
	color: #374151; /* 浅色背景按钮使用深色文字 */
}

.btn-text .btn-text-content {
	color: #ff8a00; /* 文本按钮使用品牌色文字 */
}

.btn-icon.left {
	margin-right: 8rpx; /* iOS标准图标间距 */
}

.btn-icon.right {
	margin-left: 8rpx; /* iOS标准图标间距 */
}

/* iOS风格加载动画 */
.loading-spinner {
	width: 32rpx; /* 更大的加载指示器 */
	height: 32rpx;
	border: 4rpx solid rgba(255, 255, 255, 0.2);
	border-top: 4rpx solid currentColor;
	border-radius: 50%;
	animation: iosSpinning 1s linear infinite;
}

/* iOS风格交互效果 */
.interactive-button:not(.btn-disabled):not(.btn-loading):active {
	transform: scale(0.96); /* iOS标准按压缩放 */
	transition: transform 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.interactive-button:not(.btn-disabled):not(.btn-loading):not(:active) {
	transform: scale(1);
	transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* iOS风格悬停效果（仅在支持hover的设备上） */
@media (hover: hover) {
	.interactive-button:not(.btn-disabled):not(.btn-loading):hover {
		opacity: 0.85;
		transition: opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}
}

/* iOS风格适老化样式 */
.elderly-mode {
	font-size: calc(34rpx * 1.2) !important; /* 基于iOS Body字体放大 */
	font-weight: 600 !important; /* 使用Semibold字重 */
	padding: calc(16rpx * 1.5) calc(32rpx * 1.5) !important; /* 放大内边距 */
	border-radius: 20rpx !important; /* 更大的圆角 */
	min-height: 112rpx !important; /* 更大的触摸目标 */
	box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.08) !important; /* 更明显的阴影 */
	border: 2rpx solid #b0b0b0 !important; /* 高对比度边框 */
}

.elderly-mode.btn-small {
	font-size: calc(26rpx * 1.2) !important;
	padding: calc(12rpx * 1.5) calc(24rpx * 1.5) !important;
	min-height: 88rpx !important;
}

.elderly-mode.btn-large {
	font-size: calc(36rpx * 1.2) !important;
	padding: calc(20rpx * 1.5) calc(40rpx * 1.5) !important;
	min-height: 136rpx !important;
}

/* 适老化按压效果增强 */
.elderly-mode:not(.btn-disabled):not(.btn-loading):active {
	transform: scale(0.94) !important; /* 更明显的按压效果 */
	box-shadow: 0 2rpx 16rpx rgba(255, 138, 0, 0.3) !important;
}

/* iOS风格动画关键帧 */
@keyframes iosSpinning {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/* iOS风格响应式设计 */
@media (max-width: 375px) {
	/* iPhone SE */
	.btn-small {
		padding: 10rpx 20rpx;
		font-size: 24rpx;
		min-height: 56rpx;
	}

	.btn-medium {
		padding: 14rpx 28rpx;
		font-size: 30rpx;
		min-height: 80rpx;
	}

	.btn-large {
		padding: 18rpx 36rpx;
		font-size: 32rpx;
		min-height: 104rpx;
	}
}

@media (min-width: 768px) {
	/* iPad */
	.btn-small {
		padding: 16rpx 32rpx;
		font-size: 28rpx;
		min-height: 72rpx;
	}

	.btn-medium {
		padding: 20rpx 40rpx;
		font-size: 36rpx;
		min-height: 96rpx;
	}

	.btn-large {
		padding: 24rpx 48rpx;
		font-size: 40rpx;
		min-height: 120rpx;
	}
}
</style>
