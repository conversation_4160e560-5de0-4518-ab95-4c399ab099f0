# 智慧养老项目图标使用示例

## 基础用法示例

### 1. 简单图标
```vue
<!-- 使用默认尺寸和颜色 -->
<Icon name="home-line" />

<!-- 指定尺寸 -->
<Icon name="home-line" size="48rpx" />

<!-- 指定颜色 -->
<Icon name="home-line" size="48rpx" color="#ff8a00" />
```

### 2. 主题色快捷方式
```vue
<!-- 主色调 -->
<Icon name="home-line" primary />

<!-- 机构色 -->
<Icon name="building-line" institution />

<!-- 服务色 -->
<Icon name="search-line" service />

<!-- 适老色 -->
<Icon name="user-settings-line" elderly />

<!-- 次要颜色 -->
<Icon name="settings-line" secondary />
```

### 3. 图标类型
```vue
<!-- 自动检测类型 -->
<Icon name="home-line" />

<!-- 强制使用SVG -->
<Icon name="home-line" type="svg" />

<!-- 强制使用Emoji -->
<Icon name="home-line" type="emoji" />

<!-- 使用自定义图片 -->
<Icon name="logo" type="image" src="/static/images/logo.png" />
```

## 业务场景示例

### 1. 导航栏图标
```vue
<template>
  <view class="navbar">
    <view class="nav-item">
      <Icon name="home-line" size="32rpx" primary />
      <text>首页</text>
    </view>
    <view class="nav-item">
      <Icon name="building-line" size="32rpx" institution />
      <text>机构</text>
    </view>
    <view class="nav-item">
      <Icon name="search-line" size="32rpx" service />
      <text>服务</text>
    </view>
  </view>
</template>
```

### 2. 功能卡片
```vue
<template>
  <view class="function-card">
    <view class="card-icon">
      <Icon name="building-line" size="48rpx" institution />
    </view>
    <view class="card-content">
      <text class="card-title">选机构</text>
      <text class="card-desc">查找附近养老机构</text>
    </view>
  </view>
</template>

<style>
.function-card {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: white;
  border-radius: 12rpx;
}

.card-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 100%);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}
</style>
```

### 3. 列表项图标
```vue
<template>
  <view class="list-item">
    <Icon name="location-line" size="24rpx" secondary />
    <text class="item-text">北京市朝阳区</text>
  </view>
</template>
```

### 4. 状态图标
```vue
<template>
  <view class="status-list">
    <view class="status-item">
      <Icon name="check-line" size="24rpx" color="#4cd964" />
      <text>已完成</text>
    </view>
    <view class="status-item">
      <Icon name="loading-line" size="24rpx" color="#ff8a00" class="loading" />
      <text>处理中</text>
    </view>
    <view class="status-item">
      <Icon name="error-warning-line" size="24rpx" color="#dd524d" />
      <text>失败</text>
    </view>
  </view>
</template>
```

### 5. 紧急服务按钮
```vue
<template>
  <view class="emergency-buttons">
    <view class="emergency-btn call-center" @click="callCenter">
      <Icon name="customer-service-2-line" size="48rpx" color="white" />
      <text class="btn-text">呼叫中心</text>
    </view>
    <view class="emergency-btn sos" @click="callSOS">
      <Icon name="sos-line" size="48rpx" color="white" />
      <text class="btn-text">紧急求助</text>
    </view>
  </view>
</template>

<style>
.emergency-buttons {
  display: flex;
  gap: 20rpx;
}

.emergency-btn {
  flex: 1;
  padding: 30rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15rpx;
}

.call-center {
  background: linear-gradient(135deg, #4ecdc4 0%, #26d0ce 100%);
}

.sos {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.btn-text {
  color: white;
  font-size: 28rpx;
  font-weight: bold;
}
</style>
```

## 适老化设计示例

### 1. 大图标模式
```vue
<template>
  <view class="elderly-mode" v-if="isElderlyMode">
    <view class="elderly-function">
      <Icon name="elderly-care-line" size="96rpx" elderly />
      <text class="elderly-text">老人关怀</text>
    </view>
  </view>
</template>

<style>
.elderly-function {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
  padding: 40rpx;
}

.elderly-text {
  font-size: 36rpx;
  color: #96ceb4;
  font-weight: bold;
}
</style>
```

### 2. 高对比度图标
```vue
<template>
  <view class="high-contrast">
    <Icon name="voice-line" size="64rpx" color="#000" />
    <text class="contrast-text">语音播报</text>
  </view>
</template>
```

## 动画效果示例

### 1. 加载动画
```vue
<template>
  <Icon name="loading-line" size="48rpx" primary class="loading" />
</template>

<style>
.loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
```

### 2. 悬停效果
```vue
<template>
  <view class="hover-icon" @click="handleClick">
    <Icon name="heart-3-line" size="48rpx" primary />
  </view>
</template>

<style>
.hover-icon {
  transition: transform 0.3s ease;
}

.hover-icon:hover {
  transform: scale(1.1);
}

.hover-icon:active {
  transform: scale(0.95);
}
</style>
```

## 响应式设计示例

### 1. 屏幕尺寸适配
```vue
<template>
  <view class="responsive-icon">
    <Icon name="home-line" :size="iconSize" primary />
  </view>
</template>

<script>
export default {
  computed: {
    iconSize() {
      const screenWidth = uni.getSystemInfoSync().screenWidth
      if (screenWidth < 375) {
        return '28rpx'  // 小屏幕
      } else if (screenWidth < 414) {
        return '32rpx'  // 中等屏幕
      } else {
        return '36rpx'  // 大屏幕
      }
    }
  }
}
</script>
```

### 2. 暗黑模式适配
```vue
<template>
  <view class="theme-icon">
    <Icon name="home-line" size="48rpx" :color="iconColor" />
  </view>
</template>

<script>
export default {
  computed: {
    iconColor() {
      return this.$store.state.theme === 'dark' ? '#ffffff' : '#ff8a00'
    }
  }
}
</script>
```

## 性能优化示例

### 1. 图标预加载
```javascript
// 在应用启动时预加载常用图标
const preloadIcons = [
  'home-line',
  'building-line', 
  'search-line',
  'user-settings-line'
]

preloadIcons.forEach(iconName => {
  // 预加载SVG图标
  const image = new Image()
  image.src = `/static/icons/${iconName}.svg`
})
```

### 2. 条件渲染
```vue
<template>
  <view class="conditional-icons">
    <!-- 只在需要时渲染复杂图标 -->
    <Icon v-if="showDetailIcon" name="complex-chart" size="64rpx" />
    
    <!-- 使用简单图标作为占位符 -->
    <Icon v-else name="loading-line" size="32rpx" class="loading" />
  </view>
</template>
```

## 错误处理示例

### 1. 图标加载失败处理
```vue
<template>
  <view class="safe-icon">
    <Icon 
      :name="iconName" 
      size="48rpx" 
      primary 
      @error="handleIconError"
    />
  </view>
</template>

<script>
export default {
  data() {
    return {
      iconName: 'home-line',
      fallbackIcon: 'question-line'
    }
  },
  methods: {
    handleIconError() {
      // 使用备用图标
      this.iconName = this.fallbackIcon
    }
  }
}
</script>
```

---

*这些示例展示了图标组件在智慧养老项目中的各种使用场景，请根据实际需求选择合适的用法。*
