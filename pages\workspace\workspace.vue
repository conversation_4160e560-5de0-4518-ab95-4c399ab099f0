<template>
	<view class="container fade-in" :class="{ 'elderly-mode': isElderlyMode }">
		<!-- 页面头部 -->
		<PageHeader
			title="工作台"
			:show-back="false"
			:elderly-mode="isElderlyMode"
		></PageHeader>
		<!-- 用户信息卡片 - iOS风格增强 -->
		<view class="user-card scale-in ios-press-light">
			<view class="avatar ios-transition">
				<Icon name="user-line" size="80rpx" color="white"></Icon>
			</view>
			<view class="user-info">
				<text class="username text-title2">{{userInfo.name}}</text>
				<text class="user-desc text-subheadline">{{userInfo.role}}</text>
				<view class="user-badge">
					<Icon name="vip-crown-line" size="24rpx" color="#ff8a00"></Icon>
					<text class="badge-text">VIP用户</text>
				</view>
			</view>
			<view class="user-stats">
				<view class="stat-item ios-press-light">
					<text class="stat-number text-title1">{{todayTasks}}</text>
					<text class="stat-label text-footnote">今日任务</text>
				</view>
				<view class="stat-divider"></view>
				<view class="stat-item ios-press-light">
					<text class="stat-number text-title1">{{completedTasks}}</text>
					<text class="stat-label text-footnote">已完成</text>
				</view>
			</view>
		</view>
		
		<!-- 功能模块 -->
		<view class="function-modules">
			<!-- 任务管理 -->
			<view class="module-card">
				<view class="module-header">
					<Icon name="task-line" size="40rpx" color="#ff8a00" class="module-icon"></Icon>
					<text class="module-title">任务管理</text>
					<text class="module-more" @click="navigateTo('/pages/task/list')">查看全部</text>
				</view>
				<view class="task-list">
					<view class="task-item" v-for="(task, index) in taskList" :key="index" @click="viewTask(task)">
						<view class="task-content">
							<text class="task-title">{{task.title}}</text>
							<text class="task-time">{{task.time}}</text>
						</view>
						<view class="task-status" :class="task.status">
							<text>{{getStatusText(task.status)}}</text>
						</view>
					</view>
				</view>
				<view class="module-actions">
					<button class="action-btn primary" @click="navigateTo('/pages/task/manage')">创建任务</button>
					<button class="action-btn" @click="navigateTo('/pages/task/manage')">任务管理</button>
				</view>
			</view>
			
			<!-- 日程安排 -->
			<view class="module-card">
				<view class="module-header">
					<Icon name="calendar-line" size="40rpx" color="#ff9800" class="module-icon"></Icon>
					<text class="module-title">日程安排</text>
					<text class="module-more" @click="navigateTo('/pages/schedule/calendar')">查看日历</text>
				</view>
				<view class="schedule-today">
					<text class="today-date">{{todayDate}}</text>
					<view class="schedule-list">
						<view class="schedule-item" v-for="(schedule, index) in todaySchedule" :key="index">
							<view class="schedule-time">{{schedule.time}}</view>
							<view class="schedule-content">
								<text class="schedule-title">{{schedule.title}}</text>
								<text class="schedule-location">{{schedule.location}}</text>
							</view>
						</view>
					</view>
				</view>
				<view class="module-actions">
					<button class="action-btn primary" @click="navigateTo('/pages/schedule/calendar')">新建日程</button>
					<button class="action-btn" @click="navigateTo('/pages/schedule/calendar')">日程管理</button>
				</view>
			</view>
			
			<!-- 健康监测 -->
			<view class="module-card">
				<view class="module-header">
					<Icon name="health-book-line" size="40rpx" color="#4caf50" class="module-icon"></Icon>
					<text class="module-title">健康监测</text>
					<text class="module-more" @click="navigateTo('/pages/health/index')">健康管理</text>
				</view>
				<view class="health-overview">
					<view class="health-item">
						<text class="health-label">血压</text>
						<text class="health-value">{{healthData.bloodPressure}}</text>
						<text class="health-unit">mmHg</text>
					</view>
					<view class="health-item">
						<text class="health-label">心率</text>
						<text class="health-value">{{healthData.heartRate}}</text>
						<text class="health-unit">次/分</text>
					</view>
					<view class="health-item">
						<text class="health-label">血糖</text>
						<text class="health-value">{{healthData.bloodSugar}}</text>
						<text class="health-unit">mmol/L</text>
					</view>
				</view>
				<view class="module-actions">
					<button class="action-btn primary" @click="navigateTo('/pages/health/manage')">记录数据</button>
					<button class="action-btn" @click="navigateTo('/pages/health/manage')">健康管理</button>
				</view>
			</view>

			<!-- 预约管理 -->
			<view class="module-card">
				<view class="module-header">
					<Icon name="calendar-check-line" size="40rpx" color="#2196f3" class="module-icon"></Icon>
					<text class="module-title">预约管理</text>
					<text class="module-more" @click="navigateTo('/pages/appointment/list')">查看全部</text>
				</view>
				<view class="appointment-overview">
					<view class="appointment-stats">
						<view class="stat-card">
							<text class="stat-number">{{appointmentStats.total}}</text>
							<text class="stat-label">总预约</text>
						</view>
						<view class="stat-card">
							<text class="stat-number">{{appointmentStats.pending}}</text>
							<text class="stat-label">待确认</text>
						</view>
						<view class="stat-card">
							<text class="stat-number">{{appointmentStats.confirmed}}</text>
							<text class="stat-label">已确认</text>
						</view>
					</view>
					<view class="recent-appointments">
						<view class="appointment-item" v-for="(appointment, index) in recentAppointments" :key="index" @click="viewAppointment(appointment)">
							<view class="appointment-icon" :class="appointment.status">
								<Icon :name="getAppointmentIcon(appointment.serviceType)" size="24rpx" color="white"></Icon>
							</view>
							<view class="appointment-content">
								<text class="appointment-title">{{appointment.serviceName}}</text>
								<text class="appointment-time">{{appointment.appointmentTime}}</text>
							</view>
							<view class="appointment-status" :class="appointment.status">
								<text class="status-text">{{getAppointmentStatusText(appointment.status)}}</text>
							</view>
						</view>
					</view>
				</view>
				<view class="module-actions">
					<button class="action-btn primary" @click="navigateTo('/pages/appointment/create')">新建预约</button>
					<button class="action-btn" @click="navigateTo('/pages/appointment/list')">预约管理</button>
				</view>
			</view>

			<!-- 消息通知 -->
			<view class="module-card">
				<view class="module-header">
					<Icon name="mail-line" size="40rpx" color="#9c27b0" class="module-icon"></Icon>
					<text class="module-title">消息通知</text>
					<text class="module-more" @click="navigateTo('/pages/message/list')">查看全部</text>
				</view>
				<view class="message-preview">
					<view class="message-item" v-for="(message, index) in recentMessages" :key="index" @click="viewMessage(message)">
						<view class="message-icon" :class="message.type">
							<Icon :name="getMessageIcon(message.type)" size="24rpx" color="white"></Icon>
						</view>
						<view class="message-content">
							<text class="message-title">{{message.title}}</text>
							<text class="message-time">{{message.time}}</text>
						</view>
						<view class="unread-dot" v-if="!message.isRead"></view>
					</view>
				</view>
				<view class="module-actions">
					<button class="action-btn primary" @click="navigateTo('/pages/message/list')">查看消息</button>
					<button class="action-btn" @click="markAllRead">全部已读</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import InteractionUtils from '@/utils/interactionUtils.js'
import { elderlyModeManager } from '@/utils/elderlyModeUtils.js'

export default {
	components: {
		Icon,
		PageHeader
	},
	data() {
		return {
			isElderlyMode: false, // 适老化模式状态
			userInfo: {
				name: '张大爷',
				role: '服务对象'
			},
			todayTasks: 3,
			completedTasks: 1,
			taskList: [
				{
					id: 1,
					title: '体检预约',
					time: '09:00',
					status: 'pending'
				},
				{
					id: 2,
					title: '康复训练',
					time: '14:00',
					status: 'completed'
				},
				{
					id: 3,
					title: '用药提醒',
					time: '18:00',
					status: 'pending'
				}
			],
			todayDate: '',
			todaySchedule: [
				{
					time: '09:00',
					title: '晨练',
					location: '社区公园'
				},
				{
					time: '14:30',
					title: '医生问诊',
					location: '社区医院'
				},
				{
					time: '16:00',
					title: '社交活动',
					location: '活动中心'
				}
			],
			healthData: {
				bloodPressure: '120/80',
				heartRate: '72',
				bloodSugar: '5.6'
			},
			recentMessages: [
				{
					id: 1,
					type: 'system',
					title: '系统升级通知',
					time: '14:30',
					isRead: false
				},
				{
					id: 2,
					type: 'service',
					title: '服务预约提醒',
					time: '10:15',
					isRead: false
				},
				{
					id: 3,
					type: 'health',
					title: '用药提醒',
					time: '14:00',
					isRead: true
				}
			],
			appointmentStats: {
				total: 5,
				pending: 2,
				confirmed: 3
			},
			recentAppointments: [
				{
					id: 1,
					serviceName: '居家护理服务',
					serviceType: 'nursing',
					appointmentTime: '今天 14:00',
					status: 'confirmed'
				},
				{
					id: 2,
					serviceName: '家政清洁服务',
					serviceType: 'cleaning',
					appointmentTime: '明天 09:00',
					status: 'pending'
				},
				{
					id: 3,
					serviceName: '康复理疗服务',
					serviceType: 'therapy',
					appointmentTime: '1月20日 10:30',
					status: 'confirmed'
				}
			]
		}
	},
	onLoad() {
		this.initData();
	},
	onShow() {
		this.refreshData();
		// 检查适老化模式状态
		this.checkElderlyMode();
	},
	methods: {
		initData() {
			// 获取今天日期
			const today = new Date();
			this.todayDate = `${today.getMonth() + 1}月${today.getDate()}日`;

			// 检查适老化模式
			this.checkElderlyMode();

			// 加载工作台数据
			this.loadWorkspaceData();
		},

		// 检查适老化模式状态
		checkElderlyMode() {
			this.isElderlyMode = elderlyModeManager.isElderlyMode();
		},
		loadWorkspaceData() {
			// 模拟加载数据
			console.log('加载工作台数据');
		},
		refreshData() {
			// 刷新数据
			this.loadWorkspaceData();
		},
		navigateTo(url) {
			uni.navigateTo({
				url: url
			});
		},
		createTask() {
			uni.navigateTo({
				url: '/pages/task/manage'
			});
		},
		viewTask(task) {
			uni.navigateTo({
				url: `/pages/task/detail?id=${task.id}`
			});
		},
		createSchedule() {
			uni.navigateTo({
				url: '/pages/schedule/calendar'
			});
		},
		recordHealth() {
			uni.navigateTo({
				url: '/pages/health/manage'
			});
		},
		getStatusText(status) {
			const statusMap = {
				'pending': '待完成',
				'completed': '已完成',
				'overdue': '已逾期'
			};
			return statusMap[status] || '未知';
		},
		getMessageIcon(type) {
			const iconMap = {
				'system': 'settings-line',
				'service': 'heart-3-line',
				'health': 'health-book-line',
				'activity': 'calendar-line'
			};
			return iconMap[type] || 'mail-line';
		},
		viewMessage(message) {
			message.isRead = true;
			uni.navigateTo({
				url: `/pages/message/detail?id=${message.id}`
			});
		},
		markAllRead() {
			this.recentMessages.forEach(message => {
				message.isRead = true;
			});
			uni.showToast({
				title: '已全部标记为已读',
				icon: 'success'
			});
		},
		// 预约相关方法
		getAppointmentIcon(serviceType) {
			const iconMap = {
				'nursing': 'user-heart-line',
				'cleaning': 'home-line',
				'therapy': 'health-book-line',
				'medical': 'stethoscope-line',
				'companion': 'user-smile-line'
			};
			return iconMap[serviceType] || 'calendar-line';
		},
		getAppointmentStatusText(status) {
			const statusMap = {
				'pending': '待确认',
				'confirmed': '已确认',
				'completed': '已完成',
				'cancelled': '已取消'
			};
			return statusMap[status] || '未知';
		},
		viewAppointment(appointment) {
			uni.navigateTo({
				url: `/pages/appointment/detail?id=${appointment.id}`
			});
		}
	}
}
</script>

<style scoped>
/* ================================
   iOS风格工作台样式
   基于iOS Human Interface Guidelines
   ================================ */

.container {
	background: linear-gradient(135deg, #ff8a00 0%, #f57c00 100%); /* 简化渐变 */
	min-height: 100vh;
	padding-top: 32rpx; /* iOS标准间距 */
}

/* iOS风格用户信息卡片 */
.user-card {
	background-color: rgba(255, 255, 255, 0.95);
	border-radius: 24rpx; /* iOS大圆角 */
	padding: 32rpx; /* iOS标准间距 */
	margin: 0 32rpx 32rpx; /* iOS标准间距 */
	display: flex;
	align-items: center;
	box-shadow:
		0 4rpx 16rpx rgba(0, 0, 0, 0.08),
		0 8rpx 32rpx rgba(0, 0, 0, 0.12); /* iOS分层阴影 */
	backdrop-filter: blur(20rpx); /* iOS毛玻璃效果 */
	-webkit-backdrop-filter: blur(20rpx);
}

.avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 30rpx; /* iOS大圆角，不再使用完全圆形 */
	margin-right: 24rpx; /* iOS标准间距 */
	background: linear-gradient(135deg, #ff8a00 0%, #f57c00 100%); /* 简化渐变 */
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow:
		0 2rpx 8rpx rgba(255, 138, 0, 0.2),
		0 4rpx 16rpx rgba(255, 138, 0, 0.15); /* iOS分层阴影 */
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.user-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.username {
	font-size: 40rpx; /* iOS Title 3字体 */
	font-weight: 700; /* iOS Bold字重 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
	color: #1F2937; /* iOS标准文字色 */
	letter-spacing: -0.01em;
	line-height: 1.2;
}

.user-desc {
	font-size: 30rpx; /* iOS Subheadline字体 */
	font-weight: 500; /* iOS Medium字重 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
	color: #6B7280; /* iOS次要文字色 */
	line-height: 1.3;
}

.user-badge {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 6rpx 12rpx;
	background: rgba(255, 138, 0, 0.1);
	border-radius: 12rpx;
	align-self: flex-start;
}

.badge-text {
	font-size: 22rpx; /* iOS Caption字体 */
	font-weight: 600; /* iOS Semibold字重 */
	color: #ff8a00;
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
}

.user-stats {
	display: flex;
	align-items: center;
	gap: 16rpx; /* 减少间距 */
}

.stat-item {
	text-align: center;
	padding: 20rpx 16rpx; /* 增大内边距 */
	border-radius: 16rpx; /* 更大的iOS圆角 */
	background: rgba(255, 255, 255, 0.8); /* 半透明白色背景 */
	backdrop-filter: blur(10rpx); /* 毛玻璃效果 */
	-webkit-backdrop-filter: blur(10rpx);
	min-width: 88rpx; /* 增大最小宽度 */
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06); /* iOS轻阴影 */
	transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.stat-divider {
	width: 1rpx;
	height: 40rpx;
	background: rgba(142, 142, 147, 0.3); /* iOS分割线颜色 */
}

.stat-number {
	font-size: 36rpx; /* iOS Headline字体 */
	font-weight: 700; /* iOS Bold字重 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
	color: #ff8a00; /* 保持品牌色 */
	display: block;
	line-height: 1.2;
}

.stat-label {
	font-size: 22rpx; /* iOS Caption 2字体 */
	font-weight: 400; /* iOS Regular字重 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
	color: #9CA3AF; /* iOS三级文字色 */
	margin-top: 4rpx; /* iOS标准间距 */
}

.function-modules {
	display: flex;
	flex-direction: column;
	gap: 24rpx; /* 减少间距，更紧凑 */
	padding: 0 24rpx; /* 与首页保持一致 */
	margin-bottom: 40rpx;
}

.module-card {
	background: rgba(255, 255, 255, 0.95); /* 半透明白色背景 */
	backdrop-filter: blur(20rpx); /* 毛玻璃效果 */
	-webkit-backdrop-filter: blur(20rpx);
	border-radius: 28rpx; /* 更大的iOS圆角 */
	padding: 32rpx; /* iOS标准内边距 */
	box-shadow:
		0 2rpx 8rpx rgba(0, 0, 0, 0.04),
		0 4rpx 24rpx rgba(0, 0, 0, 0.06),
		0 8rpx 40rpx rgba(0, 0, 0, 0.04); /* iOS分层阴影 */
	border: 1rpx solid rgba(255, 255, 255, 0.2); /* 添加边框 */
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.module-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 24rpx; /* iOS标准间距 */
}

.header-left {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.module-icon-container {
	width: 56rpx;
	height: 56rpx;
	border-radius: 16rpx; /* iOS圆角 */
	background: linear-gradient(135deg, #ff8a00 0%, #f57c00 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2rpx 8rpx rgba(255, 138, 0, 0.3);
}

.module-title {
	font-size: 40rpx; /* iOS Title 3字体 */
	font-weight: 700; /* iOS Bold字重 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
	color: #1F2937; /* iOS标准文字色 */
	letter-spacing: -0.01em;
	line-height: 1.2;
}

.module-more {
	display: flex;
	align-items: center;
	gap: 8rpx;
	font-size: 32rpx; /* iOS Callout字体 */
	color: #ff8a00;
	font-weight: 600; /* iOS Semibold字重 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
	padding: 8rpx 16rpx;
	border-radius: 16rpx;
	background: rgba(255, 138, 0, 0.1);
	transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.task-list {
	margin-bottom: 24rpx; /* iOS标准间距 */
}

.task-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid rgba(142, 142, 147, 0.2); /* iOS分割线 */
	transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	border-radius: 12rpx; /* 添加圆角 */
	margin-bottom: 8rpx;
}

.task-item:last-child {
	border-bottom: none;
	margin-bottom: 0;
}

.task-indicator {
	width: 8rpx;
	height: 40rpx;
	border-radius: 4rpx;
	margin-right: 16rpx;
}

.task-indicator.pending {
	background: #ff9500; /* iOS橙色 */
}

.task-indicator.completed {
	background: #34c759; /* iOS绿色 */
}

.task-indicator.overdue {
	background: #ff3b30; /* iOS红色 */
}

.task-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.task-title {
	font-size: 32rpx; /* iOS Callout字体 */
	font-weight: 600; /* iOS Semibold字重 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
	color: #1F2937; /* iOS标准文字色 */
	line-height: 1.3;
}

.task-meta {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.task-time {
	font-size: 26rpx; /* iOS Footnote字体 */
	font-weight: 500; /* iOS Medium字重 */
	color: #8e8e93; /* iOS三级文字色 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
}

.task-status {
	padding: 8rpx 16rpx;
	border-radius: 12rpx; /* iOS圆角 */
	min-width: 80rpx;
	text-align: center;
}

.status-text {
	font-size: 22rpx; /* iOS Caption字体 */
	font-weight: 600; /* iOS Semibold字重 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
}

.task-status.pending {
	background: rgba(255, 149, 0, 0.15); /* iOS橙色背景 */
}

.task-status.pending .status-text {
	color: #ff9500; /* iOS橙色文字 */
}

.task-status.completed {
	background: rgba(52, 199, 89, 0.15); /* iOS绿色背景 */
}

.task-status.completed .status-text {
	color: #34c759; /* iOS绿色文字 */
}

.task-status.overdue {
	background: rgba(255, 59, 48, 0.15); /* iOS红色背景 */
}

.task-status.overdue .status-text {
	color: #ff3b30; /* iOS红色文字 */
}

/* ================================
   iOS风格适老化增强 - 工作台
   ================================ */

/* 适老化用户卡片增强 */
.elderly-mode .user-card,
.ios-elderly-mode .user-card {
	padding: 40rpx !important; /* 增大内边距 */
	border: 2rpx solid #8e8e93 !important; /* iOS标准边框 */
}

.elderly-mode .avatar,
.ios-elderly-mode .avatar {
	width: 140rpx !important; /* 增大头像 */
	height: 140rpx !important;
	border-radius: 35rpx !important;
}

.elderly-mode .stat-item,
.ios-elderly-mode .stat-item {
	padding: 24rpx 20rpx !important; /* 增大内边距 */
	min-width: 100rpx !important; /* 增大最小宽度 */
	border: 1rpx solid #c7c7cc !important; /* iOS边框 */
}

/* 适老化模块卡片增强 */
.elderly-mode .module-card,
.ios-elderly-mode .module-card {
	padding: 40rpx !important; /* 增大内边距 */
	border: 2rpx solid #c7c7cc !important; /* iOS边框 */
	margin-bottom: 24rpx !important; /* 增加间距 */
}

.elderly-mode .module-icon-container,
.ios-elderly-mode .module-icon-container {
	width: 64rpx !important; /* 增大图标容器 */
	height: 64rpx !important;
	border-radius: 20rpx !important;
}

/* 适老化任务列表增强 */
.elderly-mode .task-item,
.ios-elderly-mode .task-item {
	padding: 24rpx 0 !important; /* 增大内边距 */
	min-height: 88rpx !important; /* iOS最小触摸目标 */
	border: 1rpx solid #e5e5ea !important; /* 添加边框 */
	border-radius: 16rpx !important; /* 更大圆角 */
	margin-bottom: 12rpx !important; /* 增加间距 */
	padding-left: 16rpx !important;
	padding-right: 16rpx !important;
}

.elderly-mode .task-indicator,
.ios-elderly-mode .task-indicator {
	width: 12rpx !important; /* 增大指示器 */
	height: 60rpx !important;
	border-radius: 6rpx !important;
}

/* 适老化焦点状态 */
.elderly-mode .task-item:focus,
.ios-elderly-mode .task-item:focus,
.elderly-mode .stat-item:focus,
.ios-elderly-mode .stat-item:focus,
.elderly-mode .module-more:focus,
.ios-elderly-mode .module-more:focus {
	outline: none !important;
	box-shadow: 0 0 0 4rpx rgba(255, 138, 0, 0.4) !important; /* 焦点环 */
	border-color: #ff8a00 !important; /* 品牌色边框 */
}

/* 适老化按压反馈增强 */
.elderly-mode .task-item:active,
.ios-elderly-mode .task-item:active {
	transform: scale(0.95) !important;
	background: rgba(255, 138, 0, 0.05) !important;
}

.elderly-mode .stat-item:active,
.ios-elderly-mode .stat-item:active {
	transform: scale(0.92) !important;
	background: rgba(255, 138, 0, 0.15) !important;
}

.module-actions {
	display: flex;
	gap: 20rpx;
}

.action-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 20rpx;
	font-size: 28rpx;
	border: none;
	background-color: #f8f9fa;
	color: #666;
	font-weight: 500;
}

.action-btn.primary {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
}

.schedule-today {
	margin-bottom: 30rpx;
}

.today-date {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 20rpx;
}

.schedule-list {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.schedule-item {
	display: flex;
	align-items: center;
	padding: 15rpx;
	background-color: #f9f9f9;
	border-radius: 10rpx;
}

.schedule-time {
	width: 120rpx;
	font-size: 24rpx;
	color: #ff8a00;
	font-weight: bold;
}

.schedule-content {
	flex: 1;
}

.schedule-title {
	font-size: 28rpx;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.schedule-location {
	font-size: 24rpx;
	color: #999;
}

.health-overview {
	display: flex;
	justify-content: space-around;
	margin-bottom: 30rpx;
}

.health-item {
	text-align: center;
}

.health-label {
	font-size: 24rpx;
	color: #999;
	display: block;
	margin-bottom: 10rpx;
}

.health-value {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.health-unit {
	font-size: 20rpx;
	color: #999;
}

.message-preview {
	margin-bottom: 30rpx;
}

.message-preview .message-item {
	display: flex;
	align-items: center;
	padding: 15rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	position: relative;
}

.message-preview .message-item:last-child {
	border-bottom: none;
}

.message-preview .message-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 15rpx;
}

.message-preview .message-icon.system {
	background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
}

.message-preview .message-icon.service {
	background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%);
}

.message-preview .message-icon.health {
	background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
}

.message-preview .message-content {
	flex: 1;
}

.message-preview .message-title {
	font-size: 26rpx;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.message-preview .message-time {
	font-size: 22rpx;
	color: #999;
}

.message-preview .unread-dot {
	position: absolute;
	top: 15rpx;
	right: 0;
	width: 12rpx;
	height: 12rpx;
	background: #f44336;
	border-radius: 50%;
}

/* ================================
   预约管理模块样式
   ================================ */

.appointment-overview {
	margin-bottom: 24rpx;
}

.appointment-stats {
	display: flex;
	gap: 16rpx;
	margin-bottom: 24rpx;
}

.stat-card {
	flex: 1;
	background: rgba(255, 255, 255, 0.8);
	backdrop-filter: blur(10rpx);
	-webkit-backdrop-filter: blur(10rpx);
	border-radius: 16rpx;
	padding: 20rpx;
	text-align: center;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
	transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.stat-card .stat-number {
	font-size: 36rpx;
	font-weight: 700;
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
	color: #2196f3;
	display: block;
	line-height: 1.2;
}

.stat-card .stat-label {
	font-size: 22rpx;
	font-weight: 400;
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
	color: #9CA3AF;
	margin-top: 4rpx;
}

.recent-appointments {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.appointment-item {
	display: flex;
	align-items: center;
	padding: 16rpx 0;
	border-bottom: 1rpx solid rgba(142, 142, 147, 0.2);
	transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	border-radius: 12rpx;
}

.appointment-item:last-child {
	border-bottom: none;
}

.appointment-icon {
	width: 48rpx;
	height: 48rpx;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
}

.appointment-icon.pending {
	background: linear-gradient(135deg, #ff9500 0%, #ff8f00 100%);
}

.appointment-icon.confirmed {
	background: linear-gradient(135deg, #34c759 0%, #30d158 100%);
}

.appointment-icon.completed {
	background: linear-gradient(135deg, #007aff 0%, #0056cc 100%);
}

.appointment-icon.cancelled {
	background: linear-gradient(135deg, #ff3b30 0%, #d70015 100%);
}

.appointment-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4rpx;
}

.appointment-title {
	font-size: 30rpx;
	font-weight: 600;
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
	color: #1F2937;
	line-height: 1.3;
}

.appointment-time {
	font-size: 24rpx;
	font-weight: 500;
	color: #8e8e93;
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
}

.appointment-status {
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
	min-width: 80rpx;
	text-align: center;
}

.appointment-status.pending {
	background: rgba(255, 149, 0, 0.15);
}

.appointment-status.pending .status-text {
	color: #ff9500;
}

.appointment-status.confirmed {
	background: rgba(52, 199, 89, 0.15);
}

.appointment-status.confirmed .status-text {
	color: #34c759;
}

.appointment-status.completed {
	background: rgba(0, 122, 255, 0.15);
}

.appointment-status.completed .status-text {
	color: #007aff;
}

.appointment-status.cancelled {
	background: rgba(255, 59, 48, 0.15);
}

.appointment-status.cancelled .status-text {
	color: #ff3b30;
}
</style>
