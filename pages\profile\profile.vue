<template>
	<view class="container fade-in" :class="{ 'elderly-mode': isElderlyMode }">
		<!-- 用户信息头部 - iOS风格增强 -->
		<view class="user-header slide-in-right">
			<view class="avatar ios-press" @click="changeAvatar">
				<Icon name="user-line" size="80rpx" color="white"></Icon>
				<view class="avatar-edit-indicator">
					<Icon name="camera-line" size="24rpx" color="white"></Icon>
				</view>
			</view>
			<view class="user-info">
				<text class="username text-title2">{{userInfo.name}}</text>
				<text class="user-phone text-subheadline">{{userInfo.phone}}</text>
				<view class="user-meta">
					<text class="user-id text-footnote">ID: {{userInfo.id}}</text>
					<view class="user-status">
						<Icon name="shield-check-line" size="20rpx" color="#34c759"></Icon>
						<text class="status-text text-caption">已认证</text>
					</view>
				</view>
			</view>
			<view class="edit-btn ios-press" @click="editProfile">
				<Icon name="edit-line" size="32rpx" color="white" class="edit-icon"></Icon>
			</view>
		</view>
		
		<!-- 快捷功能 - iOS风格增强 -->
		<view class="quick-actions scale-in">
			<view class="action-item ios-press" @click="navigateTo('/pages/order/list')">
				<view class="action-icon-container">
					<Icon name="file-list-line" size="48rpx" color="white" class="action-icon"></Icon>
					<view class="action-badge" v-if="orderCount > 0">{{orderCount}}</view>
				</view>
				<text class="action-text text-footnote">我的订单</text>
			</view>
			<view class="action-item ios-press" @click="navigateTo('/pages/favorite/list')">
				<view class="action-icon-container favorite">
					<Icon name="star-line" size="48rpx" color="white" class="action-icon"></Icon>
				</view>
				<text class="action-text text-footnote">我的收藏</text>
			</view>
			<view class="action-item ios-press" @click="navigateTo('/pages/history/list')">
				<view class="action-icon-container history">
					<Icon name="history-line" size="48rpx" color="white" class="action-icon"></Icon>
				</view>
				<text class="action-text text-footnote">浏览历史</text>
			</view>
			<view class="action-item ios-press" @click="navigateTo('/pages/wallet/wallet')">
				<view class="action-icon-container wallet">
					<Icon name="wallet-line" size="48rpx" color="white" class="action-icon"></Icon>
				</view>
				<text class="action-text text-footnote">我的钱包</text>
			</view>
		</view>
		
		<!-- 功能菜单 - iOS设置风格 -->
		<view class="menu-section slide-up">
			<view class="menu-group">
				<text class="group-title text-footnote">个人信息</text>
				<view class="menu-item ios-press-light" @click="navigateTo('/pages/profile/info')">
					<view class="menu-icon-container primary">
						<Icon name="user-line" size="32rpx" color="white" class="menu-icon"></Icon>
					</view>
					<text class="menu-text text-body">基本信息</text>
					<Icon name="arrow-right-s-line" size="20rpx" color="#c7c7cc" class="arrow-icon"></Icon>
				</view>
				<view class="menu-item ios-press-light" @click="navigateTo('/pages/profile/contact')">
					<view class="menu-icon-container success">
						<Icon name="phone-line" size="32rpx" color="white" class="menu-icon"></Icon>
					</view>
					<text class="menu-text text-body">联系方式</text>
					<Icon name="arrow-right-s-line" size="20rpx" color="#c7c7cc" class="arrow-icon"></Icon>
				</view>
				<view class="menu-item ios-press-light" @click="navigateTo('/pages/profile/emergency')">
					<view class="menu-icon-container warning">
						<Icon name="alarm-warning-line" size="32rpx" color="white" class="menu-icon"></Icon>
					</view>
					<text class="menu-text text-body">紧急联系人</text>
					<Icon name="arrow-right-s-line" size="20rpx" color="#c7c7cc" class="arrow-icon"></Icon>
				</view>
			</view>
			
			<view class="menu-group">
				<text class="group-title">账户管理</text>
				<view class="menu-item ios-press-light" @click="navigateTo('/pages/account/security')">
					<view class="menu-icon-container primary">
						<Icon name="shield-check-line" size="32rpx" color="white" class="menu-icon"></Icon>
					</view>
					<text class="menu-text text-body">安全设置</text>
					<Icon name="arrow-right-s-line" size="20rpx" color="#c7c7cc" class="arrow-icon"></Icon>
				</view>
				<view class="menu-item ios-press-light" @click="navigateTo('/pages/account/password')">
					<view class="menu-icon-container warning">
						<Icon name="lock-password-line" size="32rpx" color="white" class="menu-icon"></Icon>
					</view>
					<text class="menu-text text-body">密码管理</text>
					<Icon name="arrow-right-s-line" size="20rpx" color="#c7c7cc" class="arrow-icon"></Icon>
				</view>
				<view class="menu-item ios-press-light" @click="navigateTo('/pages/account/privacy')">
					<view class="menu-icon-container info">
						<Icon name="eye-off-line" size="32rpx" color="white" class="menu-icon"></Icon>
					</view>
					<text class="menu-text text-body">隐私设置</text>
					<Icon name="arrow-right-s-line" size="20rpx" color="#c7c7cc" class="arrow-icon"></Icon>
				</view>
			</view>
			
			<view class="menu-group">
				<text class="group-title text-footnote">应用设置</text>
				<view class="menu-item ios-press-light" @click="navigateTo('/pages/data/manage')">
					<view class="menu-icon-container primary">
						<Icon name="database-line" size="32rpx" color="white" class="menu-icon"></Icon>
					</view>
					<text class="menu-text text-body">数据管理</text>
					<Icon name="arrow-right-s-line" size="20rpx" color="#c7c7cc" class="arrow-icon"></Icon>
				</view>
				<view class="menu-item ios-press-light" @click="navigateTo('/pages/settings/notification')">
					<view class="menu-icon-container warning">
						<Icon name="notification-3-line" size="32rpx" color="white" class="menu-icon"></Icon>
					</view>
					<text class="menu-text text-body">通知设置</text>
					<Icon name="arrow-right-s-line" size="20rpx" color="#c7c7cc" class="arrow-icon"></Icon>
				</view>
				<view class="menu-item ios-press-light" @click="navigateTo('/pages/settings/display')">
					<view class="menu-icon-container info">
						<Icon name="computer-line" size="32rpx" color="white" class="menu-icon"></Icon>
					</view>
					<text class="menu-text text-body">显示设置</text>
					<Icon name="arrow-right-s-line" size="20rpx" color="#c7c7cc" class="arrow-icon"></Icon>
				</view>
				<view class="menu-item ios-press-light" @click="navigateTo('/pages/settings/language')">
					<view class="menu-icon-container success">
						<Icon name="translate-line" size="32rpx" color="white" class="menu-icon"></Icon>
					</view>
					<text class="menu-text text-body">语言设置</text>
					<Icon name="arrow-right-s-line" size="20rpx" color="#c7c7cc" class="arrow-icon"></Icon>
				</view>
			</view>
			
			<view class="menu-group">
				<text class="group-title text-footnote">帮助与反馈</text>
				<view class="menu-item ios-press-light" @click="navigateTo('/pages/help/faq')">
					<view class="menu-icon-container warning">
						<Icon name="question-line" size="32rpx" color="white" class="menu-icon"></Icon>
					</view>
					<text class="menu-text text-body">常见问题</text>
					<Icon name="arrow-right-s-line" size="20rpx" color="#c7c7cc" class="arrow-icon"></Icon>
				</view>
				<view class="menu-item ios-press-light" @click="navigateTo('/pages/help/service')">
					<view class="menu-icon-container success">
						<Icon name="customer-service-2-line" size="32rpx" color="white" class="menu-icon"></Icon>
					</view>
					<text class="menu-text text-body">在线客服</text>
					<Icon name="arrow-right-s-line" size="20rpx" color="#c7c7cc" class="arrow-icon"></Icon>
				</view>
				<view class="menu-item ios-press-light" @click="navigateTo('/pages/help/feedback')">
					<view class="menu-icon-container info">
						<Icon name="feedback-line" size="32rpx" color="white" class="menu-icon"></Icon>
					</view>
					<text class="menu-text text-body">意见反馈</text>
					<Icon name="arrow-right-s-line" size="20rpx" color="#c7c7cc" class="arrow-icon"></Icon>
				</view>
				<view class="menu-item ios-press-light" @click="navigateTo('/pages/help/about')">
					<view class="menu-icon-container primary">
						<Icon name="information-line" size="32rpx" color="white" class="menu-icon"></Icon>
					</view>
					<text class="menu-text text-body">关于我们</text>
					<Icon name="arrow-right-s-line" size="20rpx" color="#c7c7cc" class="arrow-icon"></Icon>
				</view>
			</view>
		</view>

		<!-- 开发测试区域 -->
		<view class="menu-section slide-up" style="margin-top: 20rpx;">
			<view class="menu-group">
				<text class="group-title text-footnote">开发测试</text>
				<view class="menu-item ios-press-light" @click="navigateTo('/pages/test/navigation-test')">
					<view class="menu-icon-container info">
						<Icon name="bug-line" size="32rpx" color="white" class="menu-icon"></Icon>
					</view>
					<text class="menu-text text-body">跳转功能测试</text>
					<Icon name="arrow-right-s-line" size="20rpx" color="#c7c7cc" class="arrow-icon"></Icon>
				</view>
				<view class="menu-item ios-press-light" @click="navigateTo('/pages/test/enhanced-features-test')">
					<view class="menu-icon-container success">
						<Icon name="star-line" size="32rpx" color="white" class="menu-icon"></Icon>
					</view>
					<text class="menu-text text-body">功能增强测试</text>
					<Icon name="arrow-right-s-line" size="20rpx" color="#c7c7cc" class="arrow-icon"></Icon>
				</view>
			</view>
		</view>

		<!-- 退出登录 -->
		<view class="logout-section">
			<button class="logout-btn" @click="logout">退出登录</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import InteractionUtils from '@/utils/interactionUtils.js'
import { elderlyModeManager } from '@/utils/elderlyModeUtils.js'

export default {
	components: {
		Icon
	},
	data() {
		return {
			isElderlyMode: false, // 适老化模式状态
			userInfo: {
				id: '202401001',
				name: '张大爷',
				phone: '138****5678',
				avatar: '' // 使用Icon组件显示默认头像
			},
			orderCount: 2
		}
	},
	onLoad() {
		this.loadUserInfo();
	},
	onShow() {
		this.refreshData();
		// 检查适老化模式状态
		this.checkElderlyMode();
	},
	methods: {
		loadUserInfo() {
			// 加载用户信息
			console.log('加载用户信息');
		},
		refreshData() {
			// 刷新数据
			this.loadUserInfo();
		},

		// 检查适老化模式状态
		checkElderlyMode() {
			this.isElderlyMode = elderlyModeManager.isElderlyMode();
		},

		// 统一的页面跳转方法，包含完善的错误处理
		navigateTo(url) {
			// 验证URL格式
			if (!url || typeof url !== 'string') {
				console.error('无效的跳转URL:', url);
				uni.showToast({
					title: '页面路径错误',
					icon: 'none',
					duration: 2000
				});
				return;
			}

			// 显示加载提示
			const loadingToast = uni.showLoading({
				title: '正在跳转...',
				mask: true
			});

			// 执行跳转
			uni.navigateTo({
				url: url,
				success: () => {
					console.log('页面跳转成功:', url);
					uni.hideLoading();
				},
				fail: (error) => {
					uni.hideLoading();
					console.error('页面跳转失败:', {
						url: url,
						error: error,
						errorCode: error.errCode,
						errorMsg: error.errMsg
					});

					// 根据错误类型提供不同的用户提示
					let errorMessage = '页面跳转失败';
					if (error.errCode === 4) {
						errorMessage = '页面不存在，请检查配置';
					} else if (error.errCode === -1) {
						errorMessage = '页面路径错误';
					} else if (error.errMsg) {
						errorMessage = error.errMsg;
					}

					uni.showModal({
						title: '跳转失败',
						content: `${errorMessage}\n\n路径: ${url}\n错误码: ${error.errCode || '未知'}`,
						showCancel: true,
						cancelText: '取消',
						confirmText: '重试',
						success: (res) => {
							if (res.confirm) {
								// 用户选择重试
								setTimeout(() => {
									this.navigateTo(url);
								}, 500);
							}
						}
					});
				}
			});
		},
		changeAvatar() {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					// 上传头像
					this.uploadAvatar(res.tempFilePaths[0]);
				}
			});
		},
		uploadAvatar(filePath) {
			uni.showLoading({
				title: '上传中...'
			});
			
			// 模拟上传
			setTimeout(() => {
				uni.hideLoading();
				this.userInfo.avatar = filePath;
				uni.showToast({
					title: '头像更新成功',
					icon: 'success'
				});
			}, 2000);
		},
		editProfile() {
			uni.navigateTo({
				url: '/pages/profile/info'
			});
		},
		logout() {
			uni.showModal({
				title: '确认退出',
				content: '确定要退出登录吗？',
				success: (res) => {
					if (res.confirm) {
						this.performLogout();
					}
				}
			});
		},
		performLogout() {
			// 清除用户数据
			uni.removeStorageSync('userToken');
			uni.removeStorageSync('userInfo');
			
			// 跳转到登录页
			uni.reLaunch({
				url: '/pages/login/login'
			});
		}
	}
}
</script>

<style scoped>
/* ================================
   iOS风格个人中心样式
   基于iOS Human Interface Guidelines
   ================================ */

.container {
	background: linear-gradient(135deg, #ff8a00 0%, #f57c00 100%); /* 简化渐变 */
	min-height: 100vh;
}

/* iOS风格用户头部 */
.user-header {
	background: transparent;
	padding: 40rpx 32rpx; /* iOS标准间距 */
	display: flex;
	align-items: center;
	color: white;
}

.avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 30rpx; /* iOS大圆角，不再使用完全圆形 */
	margin-right: 24rpx; /* iOS标准间距 */
	border: 3rpx solid rgba(255, 255, 255, 0.4); /* 增强边框 */
	background: rgba(255, 255, 255, 0.15); /* 半透明背景 */
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	box-shadow:
		0 2rpx 8rpx rgba(0, 0, 0, 0.1),
		0 4rpx 16rpx rgba(0, 0, 0, 0.08); /* iOS阴影 */
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.avatar-edit-indicator {
	position: absolute;
	bottom: 0;
	right: 0;
	width: 32rpx;
	height: 32rpx;
	background: rgba(0, 0, 0, 0.6);
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 2rpx solid white;
}

.user-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.username {
	font-size: 44rpx; /* iOS Title 2字体 */
	font-weight: 700; /* iOS Bold字重 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
	letter-spacing: -0.01em;
	line-height: 1.2;
}

.user-phone {
	font-size: 30rpx; /* iOS Subheadline字体 */
	font-weight: 500; /* iOS Medium字重 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
	opacity: 0.9;
	line-height: 1.3;
}

.user-meta {
	display: flex;
	align-items: center;
	gap: 16rpx;
	margin-top: 4rpx;
}

.user-id {
	font-size: 22rpx; /* iOS Caption字体 */
	font-weight: 500; /* iOS Medium字重 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
	opacity: 0.7;
}

.user-status {
	display: flex;
	align-items: center;
	gap: 6rpx;
	padding: 4rpx 8rpx;
	background: rgba(52, 199, 89, 0.2); /* iOS绿色背景 */
	border-radius: 8rpx;
}

.status-text {
	font-size: 20rpx; /* iOS Caption 2字体 */
	font-weight: 600; /* iOS Semibold字重 */
	color: #34c759; /* iOS绿色 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
}

.edit-btn {
	width: 56rpx;
	height: 56rpx;
	border-radius: 16rpx; /* iOS圆角 */
	background: rgba(255, 255, 255, 0.2);
	border: 2rpx solid rgba(255, 255, 255, 0.3);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* iOS风格快捷功能区域 */
.quick-actions {
	display: flex;
	justify-content: space-around;
	padding: 32rpx 24rpx; /* iOS标准间距 */
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(10rpx); /* 毛玻璃效果 */
	-webkit-backdrop-filter: blur(10rpx);
	margin: 0 24rpx 32rpx;
	border-radius: 24rpx; /* iOS大圆角 */
	border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.action-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 12rpx;
	padding: 16rpx 12rpx;
	border-radius: 16rpx;
	transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	position: relative;
}

.action-icon-container {
	width: 64rpx;
	height: 64rpx;
	border-radius: 18rpx; /* iOS圆角 */
	background: linear-gradient(135deg, #ff8a00 0%, #f57c00 100%); /* 默认主色 */
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow:
		0 2rpx 8rpx rgba(255, 138, 0, 0.3),
		0 4rpx 16rpx rgba(255, 138, 0, 0.2); /* iOS阴影 */
	transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	position: relative;
}

.action-icon-container.favorite {
	background: linear-gradient(135deg, #ff9500 0%, #ed8936 100%); /* iOS橙色 */
	box-shadow:
		0 2rpx 8rpx rgba(255, 149, 0, 0.3),
		0 4rpx 16rpx rgba(255, 149, 0, 0.2);
}

.action-icon-container.history {
	background: linear-gradient(135deg, #34c759 0%, #38a169 100%); /* iOS绿色 */
	box-shadow:
		0 2rpx 8rpx rgba(52, 199, 89, 0.3),
		0 4rpx 16rpx rgba(52, 199, 89, 0.2);
}

.action-icon-container.wallet {
	background: linear-gradient(135deg, #af52de 0%, #9f7aea 100%); /* iOS紫色 */
	box-shadow:
		0 2rpx 8rpx rgba(175, 82, 222, 0.3),
		0 4rpx 16rpx rgba(175, 82, 222, 0.2);
}

.action-text {
	font-size: 26rpx; /* iOS Footnote字体 */
	font-weight: 500; /* iOS Medium字重 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
	color: white;
	text-align: center;
	line-height: 1.3;
}

.action-badge {
	position: absolute;
	top: -4rpx;
	right: -4rpx;
	min-width: 24rpx;
	height: 24rpx;
	background: #ff3b30; /* iOS红色 */
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 18rpx; /* 小字体 */
	font-weight: 700; /* iOS Bold字重 */
	color: white;
	padding: 0 6rpx;
	border: 2rpx solid white;
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
}

/* iOS风格功能菜单 */
.menu-section {
	padding: 0 24rpx 40rpx; /* iOS标准间距 */
}

.menu-group {
	background: rgba(255, 255, 255, 0.95); /* 半透明白色背景 */
	backdrop-filter: blur(20rpx); /* 毛玻璃效果 */
	-webkit-backdrop-filter: blur(20rpx);
	border-radius: 28rpx; /* iOS大圆角 */
	margin-bottom: 32rpx;
	overflow: hidden;
	box-shadow:
		0 2rpx 8rpx rgba(0, 0, 0, 0.04),
		0 4rpx 24rpx rgba(0, 0, 0, 0.06),
		0 8rpx 40rpx rgba(0, 0, 0, 0.04); /* iOS分层阴影 */
	border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.group-title {
	font-size: 26rpx; /* iOS Footnote字体 */
	font-weight: 600; /* iOS Semibold字重 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
	color: #8e8e93; /* iOS三级文字色 */
	text-transform: uppercase; /* iOS风格大写 */
	letter-spacing: 0.5rpx;
	padding: 24rpx 32rpx 16rpx; /* iOS标准间距 */
	background: transparent;
}

.menu-item {
	display: flex;
	align-items: center;
	padding: 20rpx 32rpx; /* iOS标准间距 */
	background: white;
	border-bottom: 1rpx solid rgba(142, 142, 147, 0.2); /* iOS分割线 */
	transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	min-height: 88rpx; /* iOS最小触摸目标 */
}

.menu-item:last-child {
	border-bottom: none;
	border-radius: 0 0 28rpx 28rpx; /* 最后一项的圆角 */
}

.menu-item:first-child {
	border-radius: 28rpx 28rpx 0 0; /* 第一项的圆角 */
}

.menu-item:only-child {
	border-radius: 28rpx; /* 单独一项的圆角 */
}

.menu-icon-container {
	width: 48rpx;
	height: 48rpx;
	border-radius: 12rpx; /* iOS圆角 */
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx; /* iOS标准间距 */
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1); /* iOS轻阴影 */
}

.menu-icon-container.primary {
	background: linear-gradient(135deg, #ff8a00 0%, #f57c00 100%);
}

.menu-icon-container.success {
	background: linear-gradient(135deg, #34c759 0%, #38a169 100%);
}

.menu-icon-container.warning {
	background: linear-gradient(135deg, #ff3b30 0%, #e53e3e 100%);
}

.menu-icon-container.info {
	background: linear-gradient(135deg, #007aff 0%, #0056cc 100%);
}

.menu-text {
	flex: 1;
	font-size: 34rpx; /* iOS Body字体 */
	font-weight: 500; /* iOS Medium字重 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
	color: #1F2937; /* iOS标准文字色 */
	line-height: 1.3;
}

.arrow-icon {
	opacity: 0.6;
	transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* ================================
   iOS风格适老化增强 - 个人中心
   ================================ */

/* 适老化用户头部增强 */
.elderly-mode .user-header,
.ios-elderly-mode .user-header {
	padding: 48rpx 32rpx !important; /* 增大内边距 */
}

.elderly-mode .avatar,
.ios-elderly-mode .avatar {
	width: 140rpx !important; /* 增大头像 */
	height: 140rpx !important;
	border-radius: 35rpx !important;
	border: 4rpx solid rgba(255, 255, 255, 0.5) !important; /* 增强边框 */
}

.elderly-mode .avatar-edit-indicator,
.ios-elderly-mode .avatar-edit-indicator {
	width: 36rpx !important; /* 增大编辑指示器 */
	height: 36rpx !important;
	border-radius: 18rpx !important;
}

.elderly-mode .edit-btn,
.ios-elderly-mode .edit-btn {
	width: 64rpx !important; /* 增大编辑按钮 */
	height: 64rpx !important;
	border-radius: 20rpx !important;
}

/* 适老化快捷功能增强 */
.elderly-mode .quick-actions,
.ios-elderly-mode .quick-actions {
	padding: 40rpx 24rpx !important; /* 增大内边距 */
	margin: 0 24rpx 40rpx !important; /* 增加间距 */
}

.elderly-mode .action-item,
.ios-elderly-mode .action-item {
	padding: 20rpx 16rpx !important; /* 增大内边距 */
	min-height: 112rpx !important; /* 56pt触摸目标 */
	border-radius: 20rpx !important;
	border: 1rpx solid rgba(255, 255, 255, 0.3) !important; /* 添加边框 */
}

.elderly-mode .action-icon-container,
.ios-elderly-mode .action-icon-container {
	width: 72rpx !important; /* 增大图标容器 */
	height: 72rpx !important;
	border-radius: 22rpx !important;
}

/* 适老化菜单增强 */
.elderly-mode .menu-group,
.ios-elderly-mode .menu-group {
	border: 2rpx solid #c7c7cc !important; /* iOS边框 */
	margin-bottom: 40rpx !important; /* 增加间距 */
}

.elderly-mode .group-title,
.ios-elderly-mode .group-title {
	padding: 28rpx 32rpx 20rpx !important; /* 增大内边距 */
}

.elderly-mode .menu-item,
.ios-elderly-mode .menu-item {
	padding: 24rpx 32rpx !important; /* 增大内边距 */
	min-height: 112rpx !important; /* 56pt触摸目标 */
	border-bottom: 2rpx solid rgba(142, 142, 147, 0.3) !important; /* 增强分割线 */
}

.elderly-mode .menu-icon-container,
.ios-elderly-mode .menu-icon-container {
	width: 56rpx !important; /* 增大图标容器 */
	height: 56rpx !important;
	border-radius: 16rpx !important;
	margin-right: 24rpx !important; /* 增加间距 */
}

/* 适老化焦点状态 */
.elderly-mode .action-item:focus,
.ios-elderly-mode .action-item:focus,
.elderly-mode .menu-item:focus,
.ios-elderly-mode .menu-item:focus,
.elderly-mode .avatar:focus,
.ios-elderly-mode .avatar:focus,
.elderly-mode .edit-btn:focus,
.ios-elderly-mode .edit-btn:focus {
	outline: none !important;
	box-shadow: 0 0 0 4rpx rgba(255, 138, 0, 0.4) !important; /* 焦点环 */
	border-color: #ff8a00 !important; /* 品牌色边框 */
}

/* 适老化按压反馈增强 */
.elderly-mode .action-item:active,
.ios-elderly-mode .action-item:active {
	transform: scale(0.90) !important;
	background: rgba(255, 255, 255, 0.2) !important;
}

.elderly-mode .menu-item:active,
.ios-elderly-mode .menu-item:active {
	transform: scale(0.98) !important;
	background: rgba(255, 138, 0, 0.05) !important;
}

.elderly-mode .avatar:active,
.ios-elderly-mode .avatar:active,
.elderly-mode .edit-btn:active,
.ios-elderly-mode .edit-btn:active {
	transform: scale(0.92) !important;
	background: rgba(255, 255, 255, 0.3) !important;
}

.edit-btn {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.edit-icon {
	width: 40rpx;
	height: 40rpx;
}

.quick-actions {
	background-color: rgba(255, 255, 255, 0.95);
	margin: 0 40rpx 40rpx;
	border-radius: 30rpx;
	padding: 40rpx;
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.action-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	position: relative;
}

.action-icon {
	width: 60rpx;
	height: 60rpx;
	margin-bottom: 10rpx;
}

.action-text {
	font-size: 24rpx;
	color: #666;
}

.action-badge {
	position: absolute;
	top: -5rpx;
	right: 10rpx;
	background-color: #ff4757;
	color: #fff;
	font-size: 20rpx;
	padding: 2rpx 8rpx;
	border-radius: 10rpx;
	min-width: 20rpx;
	text-align: center;
}

.menu-section {
	margin: 0 40rpx;
}

.menu-group {
	background-color: white;
	border-radius: 30rpx;
	margin-bottom: 40rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.group-title {
	font-size: 28rpx;
	color: #999;
	padding: 30rpx 30rpx 20rpx;
	display: block;
}

.menu-item {
	display: flex;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
	border-bottom: none;
}

.menu-icon {
	width: 40rpx;
	height: 40rpx;
	margin-right: 20rpx;
}

.menu-text {
	flex: 1;
	font-size: 30rpx;
	color: #333;
}

.arrow-icon {
	width: 24rpx;
	height: 24rpx;
	opacity: 0.5;
}

.logout-section {
	margin: 40rpx 40rpx;
}

.logout-btn {
	width: 100%;
	height: 100rpx;
	background-color: white;
	color: #f44336;
	border: 2rpx solid #f44336;
	border-radius: 20rpx;
	font-size: 32rpx;
	font-weight: bold;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.logout-btn:active {
	background-color: #f44336;
	color: white;
}
</style>
