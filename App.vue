<script>
	import OfflineDataManager from '@/utils/offlineData.js'
	import { elderlyModeManager } from '@/utils/elderlyModeUtils.js'
	import { responsiveManager } from '@/utils/responsiveUtils.js'

	export default {
		onLaunch: function() {
			console.log('App Launch')

			// 初始化离线数据
			OfflineDataManager.initOfflineData()

			// 初始化适老化模式
			this.initElderlyMode()

			// 初始化响应式设计
			this.initResponsiveDesign()

			// 监听设置变更
			this.listenSettingsChanges()

			// 检查更新
			this.checkForUpdates()
		},
		onShow: function() {
			console.log('App Show')

			// 重新应用适老化设置
			this.applyElderlySettings()
		},
		onHide: function() {
			console.log('App Hide')
		},
		methods: {
			// 初始化适老化模式
			initElderlyMode() {
				try {
					// 适老化管理器会自动从存储中加载设置
					elderlyModeManager.init()
					console.log('适老化模式初始化完成')
				} catch (error) {
					console.error('适老化模式初始化失败:', error)
				}
			},

			// 初始化响应式设计
			initResponsiveDesign() {
				try {
					responsiveManager.init()
					console.log('响应式设计初始化完成')
				} catch (error) {
					console.error('响应式设计初始化失败:', error)
				}
			},

			// 监听设置变更
			listenSettingsChanges() {
				// 监听显示设置变更
				uni.$on('displaySettingsChanged', (settings) => {
					console.log('显示设置已更改:', settings)
					this.applyDisplaySettings(settings)
				})

				// 监听适老化设置变更
				uni.$on('elderlySettingsChanged', (settings) => {
					console.log('适老化设置已更改:', settings)
					this.applyElderlySettings(settings)
				})
			},

			// 应用显示设置
			applyDisplaySettings(settings) {
				try {
					// 这里可以添加全局显示设置的应用逻辑
					console.log('应用显示设置:', settings)
				} catch (error) {
					console.error('应用显示设置失败:', error)
				}
			},

			// 应用适老化设置
			applyElderlySettings(settings) {
				try {
					if (elderlyModeManager.isElderlyMode()) {
						elderlyModeManager.applyElderlyStyles()
					} else {
						elderlyModeManager.removeElderlyStyles()
					}
				} catch (error) {
					console.error('应用适老化设置失败:', error)
				}
			},

			checkForUpdates() {
				// 检查应用更新
				// #ifdef APP-PLUS
				plus.runtime.getProperty(plus.runtime.appid, (widgetInfo) => {
					console.log('当前版本：', widgetInfo.version)
				})
				// #endif
			}
		}
	}
</script>

<style>
	/* ================================
	   智慧养老 - iOS风格全局样式
	   基于iOS Human Interface Guidelines
	   ================================ */

	/* 重置样式 - iOS风格 */
	* {
		box-sizing: border-box;
		-webkit-tap-highlight-color: transparent; /* 移除iOS点击高亮 */
	}

	page {
		background-color: #F9FAFB; /* 使用iOS风格的页面背景色 */
		font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Helvetica, Arial, sans-serif;
		line-height: 1.47; /* iOS标准行高 */
		font-size: 34rpx; /* iOS Body字体大小 */
		color: #1F2937; /* iOS标准文字色 */
		-webkit-font-smoothing: antialiased; /* iOS字体平滑 */
		-moz-osx-font-smoothing: grayscale;
	}

	/* iOS风格CSS变量系统 */
	:root {
		/* 品牌色彩 - 保持现有 */
		--primary-color: #ff8a00;
		--primary-light: #ffb74d;
		--primary-dark: #f57c00;
		--primary-tint: rgba(255, 138, 0, 0.1);

		/* iOS系统颜色 */
		--ios-blue: #007AFF;
		--ios-green: #34C759;
		--ios-red: #FF3B30;
		--ios-orange: #FF9500;
		--ios-yellow: #FFCC00;
		--ios-purple: #AF52DE;

		/* iOS中性色系统 */
		--gray-50: #F9FAFB;
		--gray-100: #F3F4F6;
		--gray-200: #E5E7EB;
		--gray-300: #D1D5DB;
		--gray-400: #9CA3AF;
		--gray-500: #6B7280;
		--gray-600: #4B5563;
		--gray-700: #374151;
		--gray-800: #1F2937;
		--gray-900: #111827;

		/* 语义化颜色 - 更新为iOS风格 */
		--success-color: var(--ios-green);
		--warning-color: var(--ios-orange);
		--error-color: var(--ios-red);
		--info-color: var(--ios-blue);

		/* 文字颜色系统 */
		--text-color: var(--gray-800);
		--text-secondary: var(--gray-600);
		--text-tertiary: var(--gray-500);
		--text-placeholder: var(--gray-400);
		--text-disabled: var(--gray-300);

		/* 背景颜色系统 */
		--background-color: var(--gray-50);
		--card-background: #ffffff;
		--secondary-background: var(--gray-100);

		/* 边框颜色系统 */
		--border-color: var(--gray-200);
		--border-light: var(--gray-100);
		--border-dark: var(--gray-300);

		/* iOS阴影系统 */
		--shadow-xs: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
		--shadow-sm: 0 2rpx 16rpx rgba(0, 0, 0, 0.06);
		--shadow-md: 0 4rpx 24rpx rgba(0, 0, 0, 0.08);
		--shadow-lg: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
		--shadow-xl: 0 16rpx 48rpx rgba(0, 0, 0, 0.16);

		/* iOS圆角系统 */
		--radius-xs: 8rpx;
		--radius-sm: 12rpx;
		--radius-md: 16rpx;
		--radius-lg: 20rpx;
		--radius-xl: 24rpx;
		--radius-2xl: 32rpx;

		/* iOS间距系统 */
		--spacing-2: 4rpx;
		--spacing-4: 8rpx;
		--spacing-6: 12rpx;
		--spacing-8: 16rpx;
		--spacing-12: 24rpx;
		--spacing-16: 32rpx;
		--spacing-20: 40rpx;
		--spacing-24: 48rpx;
		--spacing-32: 64rpx;
		--spacing-40: 80rpx;
		--spacing-48: 96rpx;
		--spacing-64: 128rpx;
	}

	/* ================================
	   iOS风格工具类系统
	   ================================ */

	/* 布局工具类 */
	.flex {
		display: flex;
	}

	.flex-center {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.flex-between {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.flex-column {
		display: flex;
		flex-direction: column;
	}

	.flex-wrap {
		flex-wrap: wrap;
	}

	.flex-1 {
		flex: 1;
	}

	/* 文字对齐 */
	.text-center {
		text-align: center;
	}

	.text-left {
		text-align: left;
	}

	.text-right {
		text-align: right;
	}

	/* iOS风格文字颜色类 */
	.text-primary {
		color: var(--primary-color);
	}

	.text-secondary {
		color: var(--text-secondary);
	}

	.text-tertiary {
		color: var(--text-tertiary);
	}

	.text-success {
		color: var(--success-color);
	}

	.text-warning {
		color: var(--warning-color);
	}

	.text-error {
		color: var(--error-color);
	}

	.text-info {
		color: var(--info-color);
	}

	.text-disabled {
		color: var(--text-disabled);
	}

	/* iOS风格字体大小类 */
	.text-caption2 { font-size: 22rpx; }
	.text-caption1 { font-size: 24rpx; }
	.text-footnote { font-size: 26rpx; }
	.text-subheadline { font-size: 30rpx; }
	.text-callout { font-size: 32rpx; }
	.text-body { font-size: 34rpx; }
	.text-headline { font-size: 36rpx; }
	.text-title3 { font-size: 40rpx; }
	.text-title2 { font-size: 44rpx; }
	.text-title1 { font-size: 56rpx; }
	.text-large-title { font-size: 68rpx; }

	/* iOS风格字体粗细类 */
	.font-regular { font-weight: 400; }
	.font-medium { font-weight: 500; }
	.font-semibold { font-weight: 600; }
	.font-bold { font-weight: 700; }

	/* iOS风格背景色类 */
	.bg-primary {
		background-color: var(--primary-color);
	}

	.bg-white {
		background-color: #ffffff;
	}

	.bg-gray-50 {
		background-color: var(--gray-50);
	}

	.bg-gray-100 {
		background-color: var(--gray-100);
	}

	.bg-secondary {
		background-color: var(--secondary-background);
	}

	/* iOS风格圆角类 */
	.rounded-xs { border-radius: var(--radius-xs); }
	.rounded-sm { border-radius: var(--radius-sm); }
	.rounded-md { border-radius: var(--radius-md); }
	.rounded-lg { border-radius: var(--radius-lg); }
	.rounded-xl { border-radius: var(--radius-xl); }
	.rounded-2xl { border-radius: var(--radius-2xl); }
	.rounded-full { border-radius: 50%; }

	/* 兼容旧的圆角类 */
	.border-radius {
		border-radius: var(--radius-sm);
	}

	/* iOS风格阴影类 */
	.shadow-xs { box-shadow: var(--shadow-xs); }
	.shadow-sm { box-shadow: var(--shadow-sm); }
	.shadow-md { box-shadow: var(--shadow-md); }
	.shadow-lg { box-shadow: var(--shadow-lg); }
	.shadow-xl { box-shadow: var(--shadow-xl); }

	/* 兼容旧的阴影类 */
	.shadow {
		box-shadow: var(--shadow-sm);
	}

	/* iOS风格间距工具类 - 基于4pt网格系统 */
	.m-0 { margin: 0; }
	.m-1 { margin: var(--spacing-4); }  /* 4rpx */
	.m-2 { margin: var(--spacing-8); }  /* 8rpx */
	.m-3 { margin: var(--spacing-12); } /* 12rpx */
	.m-4 { margin: var(--spacing-16); } /* 16rpx */
	.m-5 { margin: var(--spacing-20); } /* 20rpx */
	.m-6 { margin: var(--spacing-24); } /* 24rpx */
	.m-8 { margin: var(--spacing-32); } /* 32rpx */

	.mt-0 { margin-top: 0; }
	.mt-1 { margin-top: var(--spacing-4); }
	.mt-2 { margin-top: var(--spacing-8); }
	.mt-3 { margin-top: var(--spacing-12); }
	.mt-4 { margin-top: var(--spacing-16); }
	.mt-5 { margin-top: var(--spacing-20); }
	.mt-6 { margin-top: var(--spacing-24); }
	.mt-8 { margin-top: var(--spacing-32); }

	.mb-0 { margin-bottom: 0; }
	.mb-1 { margin-bottom: var(--spacing-4); }
	.mb-2 { margin-bottom: var(--spacing-8); }
	.mb-3 { margin-bottom: var(--spacing-12); }
	.mb-4 { margin-bottom: var(--spacing-16); }
	.mb-5 { margin-bottom: var(--spacing-20); }
	.mb-6 { margin-bottom: var(--spacing-24); }
	.mb-8 { margin-bottom: var(--spacing-32); }

	.ml-0 { margin-left: 0; }
	.ml-1 { margin-left: var(--spacing-4); }
	.ml-2 { margin-left: var(--spacing-8); }
	.ml-3 { margin-left: var(--spacing-12); }
	.ml-4 { margin-left: var(--spacing-16); }

	.mr-0 { margin-right: 0; }
	.mr-1 { margin-right: var(--spacing-4); }
	.mr-2 { margin-right: var(--spacing-8); }
	.mr-3 { margin-right: var(--spacing-12); }
	.mr-4 { margin-right: var(--spacing-16); }

	.p-0 { padding: 0; }
	.p-1 { padding: var(--spacing-4); }
	.p-2 { padding: var(--spacing-8); }
	.p-3 { padding: var(--spacing-12); }
	.p-4 { padding: var(--spacing-16); }
	.p-5 { padding: var(--spacing-20); }
	.p-6 { padding: var(--spacing-24); }
	.p-8 { padding: var(--spacing-32); }

	.pt-0 { padding-top: 0; }
	.pt-1 { padding-top: var(--spacing-4); }
	.pt-2 { padding-top: var(--spacing-8); }
	.pt-3 { padding-top: var(--spacing-12); }
	.pt-4 { padding-top: var(--spacing-16); }
	.pt-5 { padding-top: var(--spacing-20); }
	.pt-6 { padding-top: var(--spacing-24); }
	.pt-8 { padding-top: var(--spacing-32); }

	.pb-0 { padding-bottom: 0; }
	.pb-1 { padding-bottom: var(--spacing-4); }
	.pb-2 { padding-bottom: var(--spacing-8); }
	.pb-3 { padding-bottom: var(--spacing-12); }
	.pb-4 { padding-bottom: var(--spacing-16); }
	.pb-5 { padding-bottom: var(--spacing-20); }
	.pb-6 { padding-bottom: var(--spacing-24); }
	.pb-8 { padding-bottom: var(--spacing-32); }

	.pl-0 { padding-left: 0; }
	.pl-1 { padding-left: var(--spacing-4); }
	.pl-2 { padding-left: var(--spacing-8); }
	.pl-3 { padding-left: var(--spacing-12); }
	.pl-4 { padding-left: var(--spacing-16); }

	.pr-0 { padding-right: 0; }
	.pr-1 { padding-right: var(--spacing-4); }
	.pr-2 { padding-right: var(--spacing-8); }
	.pr-3 { padding-right: var(--spacing-12); }
	.pr-4 { padding-right: var(--spacing-16); }

	/* ================================
	   iOS风格适老化系统 - 增强版
	   ================================ */

	/* 适老化模式基础样式 */
	.elderly-mode,
	.ios-elderly-mode {
		/* iOS风格字体增强 */
		font-size: calc(34rpx * 1.3) !important; /* 基于iOS Body字体放大1.3倍 */
		font-weight: 600 !important; /* 使用iOS Semibold字重 */
		line-height: 1.6 !important; /* 增加行高提升可读性 */

		/* iOS风格颜色对比度增强 */
		color: #000000 !important; /* 纯黑文字，最高对比度 */
		background-color: #ffffff !important; /* 纯白背景 */
	}

	/* 适老化字体层级 - 基于iOS Typography */
	.elderly-mode .text-caption,
	.ios-elderly-mode .text-caption {
		font-size: calc(22rpx * 1.3) !important; /* Caption * 1.3 */
		font-weight: 500 !important; /* iOS Medium */
	}

	.elderly-mode .text-footnote,
	.ios-elderly-mode .text-footnote {
		font-size: calc(26rpx * 1.3) !important; /* Footnote * 1.3 */
		font-weight: 500 !important;
	}

	.elderly-mode .text-subheadline,
	.ios-elderly-mode .text-subheadline {
		font-size: calc(30rpx * 1.3) !important; /* Subheadline * 1.3 */
		font-weight: 600 !important;
	}

	.elderly-mode .text-callout,
	.ios-elderly-mode .text-callout {
		font-size: calc(32rpx * 1.3) !important; /* Callout * 1.3 */
		font-weight: 600 !important;
	}

	.elderly-mode .text-body,
	.ios-elderly-mode .text-body {
		font-size: calc(34rpx * 1.3) !important; /* Body * 1.3 */
		font-weight: 600 !important;
	}

	.elderly-mode .text-headline,
	.ios-elderly-mode .text-headline {
		font-size: calc(36rpx * 1.3) !important; /* Headline * 1.3 */
		font-weight: 700 !important; /* iOS Bold */
	}

	.elderly-mode .text-title3,
	.ios-elderly-mode .text-title3 {
		font-size: calc(40rpx * 1.3) !important; /* Title 3 * 1.3 */
		font-weight: 700 !important;
	}

	.elderly-mode .text-title2,
	.ios-elderly-mode .text-title2 {
		font-size: calc(44rpx * 1.3) !important; /* Title 2 * 1.3 */
		font-weight: 700 !important;
	}

	.elderly-mode .text-title1,
	.ios-elderly-mode .text-title1 {
		font-size: calc(56rpx * 1.3) !important; /* Title 1 * 1.3 */
		font-weight: 700 !important;
	}

	.elderly-mode .text-large-title,
	.ios-elderly-mode .text-large-title {
		font-size: calc(68rpx * 1.3) !important; /* Large Title * 1.3 */
		font-weight: 700 !important;
	}

	/* 适老化交互元素 - iOS风格增强 */
	.elderly-mode .interactive-button,
	.ios-elderly-mode .interactive-button,
	.elderly-mode .interactive-card,
	.ios-elderly-mode .interactive-card {
		min-height: 112rpx !important; /* 增大触摸目标到56pt */
		padding: calc(var(--spacing-16) * 1.4) calc(var(--spacing-24) * 1.4) !important;
		border-radius: 25rpx !important; /* 更大的iOS风格圆角 */
		box-shadow:
			0 4rpx 16rpx rgba(0, 0, 0, 0.1),
			0 8rpx 24rpx rgba(0, 0, 0, 0.08) !important; /* 增强阴影 */
		border: 2rpx solid #8e8e93 !important; /* iOS标准边框 */
	}

	.elderly-mode .interactive-button,
	.ios-elderly-mode .interactive-button {
		font-size: calc(34rpx * 1.3) !important; /* Body * 1.3 */
		font-weight: 600 !important; /* iOS Semibold */
		letter-spacing: 0.5rpx !important; /* 增加字母间距 */
	}

	/* 适老化焦点状态 - iOS风格 */
	.elderly-mode .interactive-button:focus,
	.ios-elderly-mode .interactive-button:focus,
	.elderly-mode .interactive-card:focus,
	.ios-elderly-mode .interactive-card:focus {
		outline: none !important;
		box-shadow:
			0 4rpx 16rpx rgba(0, 0, 0, 0.1),
			0 8rpx 24rpx rgba(0, 0, 0, 0.08),
			0 0 0 4rpx rgba(255, 138, 0, 0.3) !important; /* 焦点环 */
		border-color: #ff8a00 !important; /* 品牌色边框 */
	}

	/* 适老化按压状态 - iOS风格 */
	.elderly-mode .interactive-button:active,
	.ios-elderly-mode .interactive-button:active,
	.elderly-mode .interactive-card:active,
	.ios-elderly-mode .interactive-card:active {
		transform: scale(0.92) !important; /* 更明显的按压效果 */
		box-shadow:
			0 2rpx 8rpx rgba(0, 0, 0, 0.12),
			0 4rpx 16rpx rgba(0, 0, 0, 0.1) !important; /* 按压时阴影 */
	}

	/* 适老化颜色增强 - iOS风格 */
	.elderly-mode,
	.ios-elderly-mode {
		/* 文字颜色层级 */
		--text-primary: #000000; /* 纯黑主文字 */
		--text-secondary: #2c2c2e; /* iOS深灰次要文字 */
		--text-tertiary: #48484a; /* iOS中灰三级文字 */

		/* 背景颜色层级 */
		--bg-primary: #ffffff; /* 纯白主背景 */
		--bg-secondary: #f2f2f7; /* iOS浅灰次要背景 */
		--bg-tertiary: #e5e5ea; /* iOS中灰三级背景 */

		/* 边框颜色层级 */
		--border-primary: #8e8e93; /* iOS标准边框 */
		--border-secondary: #c7c7cc; /* iOS浅边框 */
		--border-focus: #ff8a00; /* 焦点边框 */
	}

	/* 适老化边框增强 - 统一iOS风格 */
	.elderly-mode *,
	.ios-elderly-mode * {
		border-color: var(--border-primary) !important;
	}

	.elderly-mode input,
	.ios-elderly-mode input,
	.elderly-mode textarea,
	.ios-elderly-mode textarea {
		border: 2rpx solid var(--border-primary) !important;
		border-radius: 20rpx !important;
		padding: 20rpx !important;
		font-size: calc(34rpx * 1.3) !important;
		font-weight: 500 !important;
	}

	.elderly-mode input:focus,
	.ios-elderly-mode input:focus,
	.elderly-mode textarea:focus,
	.ios-elderly-mode textarea:focus {
		border-color: var(--border-focus) !important;
		box-shadow: 0 0 0 4rpx rgba(255, 138, 0, 0.3) !important;
	}

	/* 适老化图标增强 */
	.elderly-mode .icon,
	.ios-elderly-mode .icon {
		transform: scale(1.3) !important;
		filter: contrast(1.2) !important;
	}

	/* 适老化列表项增强 */
	.elderly-mode .list-item,
	.ios-elderly-mode .list-item {
		min-height: 112rpx !important;
		padding: 24rpx !important;
		border-bottom: 2rpx solid var(--border-secondary) !important;
	}

	/* 适老化导航栏增强 */
	.elderly-mode .navbar,
	.ios-elderly-mode .navbar {
		height: 120rpx !important;
		padding: 0 32rpx !important;
	}

	.elderly-mode .navbar-title,
	.ios-elderly-mode .navbar-title {
		font-size: calc(36rpx * 1.3) !important;
		font-weight: 700 !important;
	}

	/* 适老化标签页增强 */
	.elderly-mode .tab-bar,
	.ios-elderly-mode .tab-bar {
		height: 120rpx !important;
		padding: 16rpx 0 !important;
	}

	.elderly-mode .tab-item,
	.ios-elderly-mode .tab-item {
		min-height: 88rpx !important;
		padding: 12rpx 8rpx !important;
	}

	.elderly-mode .tab-text,
	.ios-elderly-mode .tab-text {
		font-size: calc(24rpx * 1.3) !important;
		font-weight: 600 !important;
		margin-top: 8rpx !important;
	}

	/* ================================
	   iOS风格响应式设计系统
	   基于iOS Human Interface Guidelines
	   ================================ */

	/* 响应式容器 */
	.responsive-container {
		width: 100%;
		margin: 0 auto;
		padding: 0 var(--container-padding, 24rpx);
	}

	/* 小屏设备 (iPhone SE) */
	@media screen and (max-width: 375px) {
		.responsive-container {
			--container-padding: 16rpx;
			max-width: 100%;
		}

		.responsive-small {
			display: block;
		}

		.responsive-medium,
		.responsive-large,
		.responsive-xlarge {
			display: none;
		}

		/* 小屏字体缩放 */
		.responsive-text {
			font-size: calc(var(--base-font-size, 32rpx) * 0.9);
		}

		/* 小屏间距缩放 */
		.responsive-spacing {
			padding: calc(var(--base-padding, 24rpx) * 0.8);
			margin: calc(var(--base-margin, 16rpx) * 0.8);
		}

		/* 小屏网格 */
		.responsive-grid {
			grid-template-columns: repeat(2, 1fr);
			gap: 12rpx;
		}

		/* 小屏按钮 */
		.responsive-button {
			height: 72rpx;
			padding: 16rpx 24rpx;
			font-size: 28rpx;
			border-radius: 12rpx;
		}

		/* 小屏卡片 */
		.responsive-card {
			padding: 24rpx;
			border-radius: 16rpx;
			min-height: 120rpx;
		}
	}

	/* 标准设备 (iPhone 12/13/14) */
	@media screen and (min-width: 376px) and (max-width: 767px) {
		.responsive-container {
			--container-padding: 24rpx;
			max-width: 100%;
		}

		.responsive-medium {
			display: block;
		}

		.responsive-small,
		.responsive-large,
		.responsive-xlarge {
			display: none;
		}

		/* 标准字体 */
		.responsive-text {
			font-size: var(--base-font-size, 32rpx);
		}

		/* 标准间距 */
		.responsive-spacing {
			padding: var(--base-padding, 24rpx);
			margin: var(--base-margin, 16rpx);
		}

		/* 标准网格 */
		.responsive-grid {
			grid-template-columns: repeat(3, 1fr);
			gap: 16rpx;
		}

		/* 标准按钮 */
		.responsive-button {
			height: 88rpx;
			padding: 20rpx 32rpx;
			font-size: 32rpx;
			border-radius: 16rpx;
		}

		/* 标准卡片 */
		.responsive-card {
			padding: 32rpx;
			border-radius: 20rpx;
			min-height: 140rpx;
		}
	}

	/* 大屏设备 (iPad) */
	@media screen and (min-width: 768px) and (max-width: 1023px) {
		.responsive-container {
			--container-padding: 32rpx;
			max-width: 1024rpx;
		}

		.responsive-large {
			display: block;
		}

		.responsive-small,
		.responsive-medium,
		.responsive-xlarge {
			display: none;
		}

		/* 大屏字体放大 */
		.responsive-text {
			font-size: calc(var(--base-font-size, 32rpx) * 1.2);
		}

		/* 大屏间距放大 */
		.responsive-spacing {
			padding: calc(var(--base-padding, 24rpx) * 1.3);
			margin: calc(var(--base-margin, 16rpx) * 1.3);
		}

		/* 大屏网格 */
		.responsive-grid {
			grid-template-columns: repeat(4, 1fr);
			gap: 24rpx;
		}

		/* 大屏按钮 */
		.responsive-button {
			height: 96rpx;
			padding: 24rpx 40rpx;
			font-size: 36rpx;
			border-radius: 20rpx;
		}

		/* 大屏卡片 */
		.responsive-card {
			padding: 40rpx;
			border-radius: 24rpx;
			min-height: 160rpx;
		}

		/* iPad专用布局 */
		.ipad-layout {
			display: flex;
			max-width: 1024rpx;
			margin: 0 auto;
		}

		.ipad-sidebar {
			width: 320rpx;
			flex-shrink: 0;
		}

		.ipad-main {
			flex: 1;
			padding-left: 32rpx;
		}
	}

	/* 超大屏设备 (iPad Pro) */
	@media screen and (min-width: 1024px) {
		.responsive-container {
			--container-padding: 48rpx;
			max-width: 1366rpx;
		}

		.responsive-xlarge {
			display: block;
		}

		.responsive-small,
		.responsive-medium,
		.responsive-large {
			display: none;
		}

		/* 超大屏字体放大 */
		.responsive-text {
			font-size: calc(var(--base-font-size, 32rpx) * 1.4);
		}

		/* 超大屏间距放大 */
		.responsive-spacing {
			padding: calc(var(--base-padding, 24rpx) * 1.6);
			margin: calc(var(--base-margin, 16rpx) * 1.6);
		}

		/* 超大屏网格 */
		.responsive-grid {
			grid-template-columns: repeat(6, 1fr);
			gap: 32rpx;
		}

		/* 超大屏按钮 */
		.responsive-button {
			height: 112rpx;
			padding: 28rpx 48rpx;
			font-size: 40rpx;
			border-radius: 24rpx;
		}

		/* 超大屏卡片 */
		.responsive-card {
			padding: 48rpx;
			border-radius: 28rpx;
			min-height: 180rpx;
		}

		/* iPad Pro专用布局 */
		.ipad-pro-layout {
			display: grid;
			grid-template-columns: 320rpx 1fr 320rpx;
			gap: 48rpx;
			max-width: 1366rpx;
			margin: 0 auto;
		}

		.ipad-pro-sidebar {
			background: rgba(255, 255, 255, 0.95);
			border-radius: 28rpx;
			padding: 32rpx;
		}

		.ipad-pro-main {
			background: rgba(255, 255, 255, 0.95);
			border-radius: 28rpx;
			padding: 40rpx;
		}
	}

	/* ================================
	   iOS风格动画系统 - 增强版
	   ================================ */

	/* iOS标准动画 */
	.fade-in {
		animation: iosFadeIn 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.slide-up {
		animation: iosSlideUp 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.slide-in-right {
		animation: iosSlideInRight 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.slide-in-left {
		animation: iosSlideInLeft 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.slide-out-left {
		animation: iosSlideOutLeft 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.slide-out-right {
		animation: iosSlideOutRight 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.scale-in {
		animation: iosScaleIn 0.25s cubic-bezier(0.175, 0.885, 0.32, 1.275);
	}

	.scale-out {
		animation: iosScaleOut 0.2s cubic-bezier(0.42, 0, 1, 1);
	}

	.bounce-in {
		animation: iosBounceIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
	}

	.shake {
		animation: iosShake 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.pulse {
		animation: iosPulse 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;
	}

	/* iOS动画关键帧 - 增强版 */
	@keyframes iosFadeIn {
		from {
			opacity: 0;
		}
		to {
			opacity: 1;
		}
	}

	@keyframes iosSlideUp {
		from {
			transform: translateY(20rpx);
			opacity: 0;
		}
		to {
			transform: translateY(0);
			opacity: 1;
		}
	}

	@keyframes iosSlideInRight {
		from {
			transform: translateX(100%);
			opacity: 0;
		}
		to {
			transform: translateX(0);
			opacity: 1;
		}
	}

	@keyframes iosSlideInLeft {
		from {
			transform: translateX(-100%);
			opacity: 0;
		}
		to {
			transform: translateX(0);
			opacity: 1;
		}
	}

	@keyframes iosSlideOutLeft {
		from {
			transform: translateX(0);
			opacity: 1;
		}
		to {
			transform: translateX(-100%);
			opacity: 0;
		}
	}

	@keyframes iosSlideOutRight {
		from {
			transform: translateX(0);
			opacity: 1;
		}
		to {
			transform: translateX(100%);
			opacity: 0;
		}
	}

	@keyframes iosScaleIn {
		from {
			transform: scale(0.8);
			opacity: 0;
		}
		50% {
			transform: scale(1.05);
			opacity: 0.8;
		}
		to {
			transform: scale(1);
			opacity: 1;
		}
	}

	@keyframes iosScaleOut {
		from {
			transform: scale(1);
			opacity: 1;
		}
		to {
			transform: scale(0.8);
			opacity: 0;
		}
	}

	@keyframes iosBounceIn {
		0% {
			transform: scale(0.3);
			opacity: 0;
		}
		50% {
			transform: scale(1.1);
			opacity: 0.8;
		}
		70% {
			transform: scale(0.9);
			opacity: 0.9;
		}
		100% {
			transform: scale(1);
			opacity: 1;
		}
	}

	@keyframes iosShake {
		0%, 100% {
			transform: translateX(0);
		}
		10%, 30%, 50%, 70%, 90% {
			transform: translateX(-10rpx);
		}
		20%, 40%, 60%, 80% {
			transform: translateX(10rpx);
		}
	}

	@keyframes iosPulse {
		0%, 100% {
			transform: scale(1);
			opacity: 1;
		}
		50% {
			transform: scale(1.05);
			opacity: 0.7;
		}
	}

	/* iOS风格过渡效果 - 增强版 */
	.ios-transition {
		transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.ios-transition-fast {
		transition: all 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.ios-transition-slow {
		transition: all 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.ios-transition-spring {
		transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
	}

	/* iOS风格微交互效果 */
	.ios-press {
		transition: transform 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.ios-press:active {
		transform: scale(0.95);
	}

	.ios-press-light {
		transition: transform 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.ios-press-light:active {
		transform: scale(0.98);
	}

	.ios-hover {
		transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.ios-hover:hover {
		transform: translateY(-2rpx);
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	}

	/* iOS风格加载动画 */
	.ios-loading {
		animation: iosLoadingRotate 1s linear infinite;
	}

	@keyframes iosLoadingRotate {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}

	/* iOS风格心跳动画 */
	.ios-heartbeat {
		animation: iosHeartbeat 1.5s ease-in-out infinite;
	}

	@keyframes iosHeartbeat {
		0%, 100% {
			transform: scale(1);
		}
		50% {
			transform: scale(1.1);
		}
	}

	/* iOS风格闪烁动画 */
	.ios-blink {
		animation: iosBlink 1s ease-in-out infinite;
	}

	@keyframes iosBlink {
		0%, 50%, 100% {
			opacity: 1;
		}
		25%, 75% {
			opacity: 0.3;
		}
	}

	/* iOS风格弹性动画 */
	.ios-elastic {
		animation: iosElastic 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
	}

	@keyframes iosElastic {
		0% {
			transform: scale(1);
		}
		30% {
			transform: scale(1.25);
		}
		40% {
			transform: scale(0.75);
		}
		60% {
			transform: scale(1.15);
		}
		80% {
			transform: scale(0.95);
		}
		100% {
			transform: scale(1);
		}
	}

	/* ================================
	   iOS风格响应式设计系统
	   ================================ */

	/* iOS设备断点 */
	@media (max-width: 375px) {
		/* iPhone SE */
		.hide-mobile {
			display: none !important;
		}

		.text-responsive {
			font-size: clamp(28rpx, 4vw, 32rpx);
		}
	}

	@media (min-width: 376px) and (max-width: 414px) {
		/* 标准iPhone */
		.text-responsive {
			font-size: clamp(30rpx, 4vw, 34rpx);
		}
	}

	@media (min-width: 415px) and (max-width: 480px) {
		/* 大屏iPhone */
		.text-responsive {
			font-size: clamp(32rpx, 4vw, 36rpx);
		}
	}

	@media (min-width: 768px) {
		/* iPad */
		.hide-desktop {
			display: none !important;
		}

		.text-responsive {
			font-size: clamp(34rpx, 3vw, 40rpx);
		}

		/* iPad布局优化 */
		.container {
			max-width: 1024px;
			margin: 0 auto;
		}
	}

	/* 兼容原有响应式类 */
	@media (max-width: 750rpx) {
		.hide-mobile {
			display: none !important;
		}
	}

	@media (min-width: 751rpx) {
		.hide-desktop {
			display: none !important;
		}
	}

	/* iOS风格滚动优化 */
	.ios-scroll {
		-webkit-overflow-scrolling: touch;
		scroll-behavior: smooth;
	}

	/* iOS风格毛玻璃效果 */
	.ios-blur {
		backdrop-filter: blur(20rpx);
		-webkit-backdrop-filter: blur(20rpx);
		background: rgba(255, 255, 255, 0.9);
	}

	/* iOS风格安全区域适配 */
	.safe-area-top {
		padding-top: env(safe-area-inset-top);
	}

	.safe-area-bottom {
		padding-bottom: env(safe-area-inset-bottom);
	}

	.safe-area-left {
		padding-left: env(safe-area-inset-left);
	}

	.safe-area-right {
		padding-right: env(safe-area-inset-right);
	}
</style>
