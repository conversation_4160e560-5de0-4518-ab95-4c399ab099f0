<template>
	<view class="container">
		<!-- 页面头部 -->
		<PageHeader title="领补贴" />

		<!-- 顶部统计 -->
		<view class="stats-section">
			<view class="stats-card">
				<text class="stats-number">{{subsidyStats.available}}</text>
				<text class="stats-label">可申请补贴</text>
			</view>
			<view class="stats-card">
				<text class="stats-number">{{subsidyStats.applied}}</text>
				<text class="stats-label">已申请</text>
			</view>
			<view class="stats-card">
				<text class="stats-number">{{subsidyStats.received}}</text>
				<text class="stats-label">已领取</text>
			</view>
		</view>
		
		<!-- 分类筛选 -->
		<view class="category-section">
			<scroll-view scroll-x="true" class="category-scroll">
				<view 
					class="category-item"
					:class="{ active: activeCategory === category.key }"
					v-for="(category, index) in categoryList"
					:key="index"
					@click="selectCategory(category.key)"
				>
					<text>{{category.name}}</text>
				</view>
			</scroll-view>
		</view>
		
		<!-- 补贴列表 -->
		<scroll-view 
			scroll-y="true" 
			class="subsidy-list"
			@scrolltolower="loadMore"
			refresher-enabled="true"
			@refresherrefresh="refreshData"
			:refresher-triggered="refreshing"
		>
			<view 
				class="subsidy-item" 
				v-for="(item, index) in subsidyList" 
				:key="index"
				@click="viewDetail(item)"
			>
				<view class="subsidy-header">
					<text class="subsidy-title">{{item.title}}</text>
					<view class="subsidy-status" :class="item.status">
						<text>{{getStatusText(item.status)}}</text>
					</view>
				</view>
				<text class="subsidy-desc">{{item.description}}</text>
				<view class="subsidy-info">
					<view class="info-item">
						<text class="info-label">补贴金额：</text>
						<text class="info-value amount">{{item.amountText || '¥' + item.amount + '/月'}}</text>
					</view>
					<view class="info-item">
						<text class="info-label">申请截止：</text>
						<text class="info-value">{{item.deadline}}</text>
					</view>
					<view class="info-item">
						<text class="info-label">申请方式：</text>
						<text class="info-value">{{item.applyMethod || '线上申请'}}</text>
					</view>
					<view class="info-item">
						<text class="info-label">办理时长：</text>
						<text class="info-value">{{item.processTime || '15个工作日'}}</text>
					</view>
				</view>
				<view class="subsidy-condition" v-if="item.condition">
					<text class="condition-label">申请条件：</text>
					<text class="condition-text">{{item.condition}}</text>
				</view>
				<view class="subsidy-actions">
					<button 
						class="action-btn"
						:class="{ primary: item.status === 'available' }"
						@click.stop="handleAction(item)"
						:disabled="item.status === 'expired'"
					>
						{{getActionText(item.status)}}
					</button>
				</view>
			</view>
			
			<!-- 加载更多 -->
			<view class="load-more" v-if="hasMore">
				<text v-if="loading">加载中...</text>
				<text v-else>上拉加载更多</text>
			</view>
			<view class="no-more" v-else-if="subsidyList.length > 0">
				<text>没有更多数据了</text>
			</view>
			<view class="empty" v-else-if="!loading">
				<image src="/static/empty/no-subsidy.png" class="empty-image"></image>
				<text class="empty-text">暂无补贴信息</text>
			</view>
		</scroll-view>
		
		<!-- 我的申请入口 -->
		<view class="my-applications">
			<button class="my-app-btn" @click="viewMyApplications">
				<image src="/static/icons/my-applications.png" class="btn-icon"></image>
				<text>我的申请</text>
			</button>
		</view>
	</view>
</template>

<script>
import FeedbackUtils from '@/utils/feedback.js'
import MockAPI from '@/utils/mockData.js'
import OfflineDataManager from '@/utils/offlineData.js'
import PageHeader from '@/components/PageHeader/PageHeader.vue'

export default {
	components: {
		PageHeader
	},
	data() {
		return {
			subsidyStats: {
				available: 8,
				applied: 3,
				received: 2
			},
			activeCategory: '',
			categoryList: [
				{ key: '', name: '全部' },
				{ key: '生活补贴', name: '生活补贴' },
				{ key: '服务补贴', name: '服务补贴' },
				{ key: '改造补贴', name: '改造补贴' },
				{ key: '医疗补贴', name: '医疗补贴' },
				{ key: '机构补贴', name: '机构补贴' },
				{ key: '康复补贴', name: '康复补贴' }
			],
			subsidyList: [],
			loading: false,
			refreshing: false,
			hasMore: true,
			page: 1,
			pageSize: 10
		}
	},
	onLoad() {
		// 初始化离线数据
		OfflineDataManager.initOfflineData();
		this.loadSubsidies();
		this.loadStats();
	},
	methods: {
		async loadSubsidies() {
			if (this.loading) return;

			// 直接使用离线数据，确保100%可用性
			try {
				const params = {
					page: this.page,
					pageSize: this.pageSize,
					category: this.activeCategory === 'all' ? undefined : this.activeCategory
				};

				const result = OfflineDataManager.getOfflineSubsidies(params);

				if (this.page === 1) {
					this.subsidyList = result.data;
				} else {
					this.subsidyList.push(...result.data);
				}

				this.hasMore = result.data.length === this.pageSize;
				this.loading = false;
				this.refreshing = false;

				// 显示成功提示
				if (this.page === 1 && result.data.length > 0) {
					FeedbackUtils.showSuccess(`已加载 ${result.data.length} 个补贴政策`);
				}
			} catch (error) {
				this.loading = false;
				this.refreshing = false;
				FeedbackUtils.showError('数据加载失败，请重试');
				console.error('加载补贴数据失败:', error);
			}
		},
		loadStats() {
			// 加载统计数据
			console.log('加载统计数据');
		},
		selectCategory(category) {
			this.activeCategory = category;
			this.page = 1;
			this.hasMore = true;
			this.loadSubsidies();
		},
		refreshData() {
			this.refreshing = true;
			this.page = 1;
			this.hasMore = true;
			this.loadSubsidies();
			this.loadStats();
		},
		loadMore() {
			if (this.hasMore && !this.loading) {
				this.page++;
				this.loadSubsidies();
			}
		},
		viewDetail(item) {
			uni.navigateTo({
				url: `/pages/subsidy/detail?id=${item.id}`
			});
		},
		handleAction(item) {
			switch (item.status) {
				case 'available':
					this.applySubsidy(item);
					break;
				case 'applied':
					this.viewApplication(item);
					break;
				case 'received':
					this.viewRecord(item);
					break;
				default:
					break;
			}
		},
		applySubsidy(item) {
			uni.navigateTo({
				url: `/pages/subsidy/apply?id=${item.id}`
			});
		},
		viewApplication(item) {
			uni.navigateTo({
				url: `/pages/subsidy/application?id=${item.id}`
			});
		},
		viewRecord(item) {
			uni.navigateTo({
				url: `/pages/subsidy/record?id=${item.id}`
			});
		},
		viewMyApplications() {
			uni.navigateTo({
				url: '/pages/subsidy/my-applications'
			});
		},
		getStatusText(status) {
			const statusMap = {
				'available': '可申请',
				'applied': '已申请',
				'received': '已领取',
				'expired': '已过期'
			};
			return statusMap[status] || '未知';
		},
		getActionText(status) {
			const actionMap = {
				'available': '立即申请',
				'applied': '查看申请',
				'received': '查看记录',
				'expired': '已过期'
			};
			return actionMap[status] || '查看详情';
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

.stats-section {
	background-color: #fff;
	margin: 20rpx;
	border-radius: 20rpx;
	padding: 30rpx;
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 30rpx;
}

.stats-card {
	text-align: center;
}

.stats-number {
	font-size: 48rpx;
	font-weight: bold;
	color: #ff8a00;
	display: block;
	margin-bottom: 10rpx;
}

.stats-label {
	font-size: 24rpx;
	color: #999;
}

.category-section {
	background-color: #fff;
	margin: 0 20rpx 20rpx;
	border-radius: 20rpx;
	padding: 20rpx 0;
}

.category-scroll {
	white-space: nowrap;
	padding: 0 30rpx;
}

.category-item {
	display: inline-block;
	padding: 15rpx 30rpx;
	margin-right: 20rpx;
	border-radius: 25rpx;
	font-size: 26rpx;
	color: #666;
	background-color: #f9f9f9;
}

.category-item.active {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: #fff;
}

.subsidy-list {
	flex: 1;
	padding: 0 20rpx 40rpx;
	min-height: 0;
}

.subsidy-item {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.subsidy-header {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
}

.subsidy-title {
	flex: 1;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.subsidy-status {
	padding: 8rpx 16rpx;
	border-radius: 15rpx;
	font-size: 22rpx;
}

.subsidy-status.available {
	background-color: #e8f5e8;
	color: #4caf50;
}

.subsidy-status.applied {
	background-color: #fff3cd;
	color: #856404;
}

.subsidy-status.received {
	background-color: #d4edda;
	color: #155724;
}

.subsidy-status.expired {
	background-color: #f8d7da;
	color: #721c24;
}

.subsidy-desc {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	margin-bottom: 20rpx;
}

.subsidy-info {
	margin-bottom: 15rpx;
}

.subsidy-condition {
	display: flex;
	align-items: flex-start;
	margin-bottom: 20rpx;
	padding: 15rpx;
	background-color: #f8f9fa;
	border-radius: 10rpx;
}

.condition-label {
	font-size: 24rpx;
	color: #666;
	width: 120rpx;
	flex-shrink: 0;
}

.condition-text {
	flex: 1;
	font-size: 24rpx;
	color: #333;
	line-height: 1.4;
}

.info-item {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}

.info-label {
	font-size: 24rpx;
	color: #999;
	width: 160rpx;
}

.info-value {
	font-size: 24rpx;
	color: #333;
}

.info-value.amount {
	color: #ff6b35;
	font-weight: bold;
	font-size: 28rpx;
}

.subsidy-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 10rpx;
	margin-bottom: 20rpx;
}

.tag {
	padding: 6rpx 12rpx;
	background-color: rgba(255, 138, 0, 0.1);
	color: #ff8a00;
	font-size: 20rpx;
	border-radius: 12rpx;
}

.subsidy-actions {
	display: flex;
	justify-content: flex-end;
}

.action-btn {
	padding: 15rpx 30rpx;
	border-radius: 25rpx;
	font-size: 26rpx;
	border: 1rpx solid #ddd;
	background-color: #fff;
	color: #666;
}

.action-btn.primary {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: #fff;
	border-color: #ff8a00;
}

.action-btn:disabled {
	background-color: #f5f5f5;
	color: #ccc;
	border-color: #eee;
}

.load-more, .no-more {
	text-align: center;
	padding: 30rpx;
	font-size: 28rpx;
	color: #999;
}

.empty {
	text-align: center;
	padding: 100rpx 0;
}

.empty-image {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 30rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

.my-applications {
	position: fixed;
	bottom: 30rpx;
	right: 30rpx;
}

.my-app-btn {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: #fff;
	border: none;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	font-size: 22rpx;
	box-shadow: 0 4rpx 20rpx rgba(255, 138, 0, 0.3);
}

.btn-icon {
	width: 40rpx;
	height: 40rpx;
	margin-bottom: 5rpx;
}
</style>
