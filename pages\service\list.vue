<template>
	<view class="container">
		<!-- 页面头部 -->
		<PageHeader title="找服务" />

		<!-- 搜索栏 -->
		<view class="search-section">
			<view class="search-bar">
				<input 
					class="search-input" 
					placeholder="搜索服务名称或关键词" 
					v-model="searchKeyword"
					@confirm="searchService"
				/>
				<button class="search-btn" @click="searchService">搜索</button>
			</view>
		</view>
		
		<!-- 服务分类 -->
		<view class="category-section">
			<scroll-view scroll-x="true" class="category-scroll">
				<view
					class="category-item"
					:class="{ active: activeCategory === category.key }"
					v-for="(category, index) in categoryList"
					:key="index"
					@click="selectCategory(category.key)"
				>
					<view class="category-icon-wrapper">
						<Icon :name="category.iconName" size="32rpx" :color="activeCategory === category.key ? '#fff' : '#666'" />
					</view>
					<text class="category-text">{{category.name}}</text>
				</view>
			</scroll-view>
		</view>
		
		<!-- 服务列表 -->
		<scroll-view 
			scroll-y="true" 
			class="service-list"
			@scrolltolower="loadMore"
			refresher-enabled="true"
			@refresherrefresh="refreshData"
			:refresher-triggered="refreshing"
		>
			<view 
				class="service-item" 
				v-for="(item, index) in serviceList" 
				:key="index"
				@click="viewDetail(item)"
			>
				<image :src="item.image" class="service-image"></image>
				<view class="service-content">
					<view class="service-header">
						<text class="service-name">{{item.name}}</text>
						<view class="service-rating">
							<text class="rating-score">{{item.rating}}</text>
							<text class="rating-text">分</text>
						</view>
					</view>
					<text class="service-desc">{{item.description}}</text>
					<view class="service-provider">
						<text class="provider-label">服务机构：</text>
						<text class="provider-name">{{item.provider}}</text>
					</view>
					<view class="service-info">
						<view class="info-item">
							<text class="info-label">价格：</text>
							<text class="info-value price">{{item.priceText || '¥' + item.price + '/小时'}}</text>
						</view>
						<view class="info-item">
							<text class="info-label">服务时间：</text>
							<text class="info-value">{{item.serviceTime || '8:00-18:00'}}</text>
						</view>
					</view>
					<view class="service-coverage" v-if="item.coverage">
						<text class="coverage-label">服务范围：</text>
						<text class="coverage-value">{{item.coverage}}</text>
					</view>
					<view class="service-tags">
						<text class="tag" v-for="(tag, tagIndex) in item.tags" :key="tagIndex">{{tag}}</text>
					</view>
					<view class="service-actions">
						<button class="action-btn" @click.stop="contactProvider(item)">联系服务商</button>
						<button class="action-btn primary" @click.stop="bookService(item)">立即预约</button>
					</view>
				</view>
			</view>
			
			<!-- 加载更多 -->
			<view class="load-more" v-if="hasMore">
				<text v-if="loading">加载中...</text>
				<text v-else>上拉加载更多</text>
			</view>
			<view class="no-more" v-else-if="serviceList.length > 0">
				<text>没有更多数据了</text>
			</view>
			<view class="empty" v-else-if="!loading">
				<view class="empty-icon">
					<Icon name="service-line" size="120rpx" color="#ccc" />
				</view>
				<text class="empty-text">暂无服务信息</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
import FeedbackUtils from '@/utils/feedback.js'
import MockAPI from '@/utils/mockData.js'
import OfflineDataManager from '@/utils/offlineData.js'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		PageHeader,
		Icon
	},
	data() {
		return {
			searchKeyword: '',
			activeCategory: '',
			categoryList: [
				{ key: '', name: '全部', iconName: 'apps-line' },
				{ key: '医疗护理', name: '医疗护理', iconName: 'health-book-line' },
				{ key: '生活照料', name: '生活照料', iconName: 'home-heart-line' },
				{ key: '康复训练', name: '康复训练', iconName: 'wheelchair-line' },
				{ key: '心理关怀', name: '心理关怀', iconName: 'heart-3-line' },
				{ key: '法律服务', name: '法律服务', iconName: 'scales-3-line' },
				{ key: '紧急服务', name: '紧急服务', iconName: 'emergency-line' }
			],
			serviceList: [],
			loading: false,
			refreshing: false,
			hasMore: true,
			page: 1,
			pageSize: 10
		}
	},
	onLoad() {
		// 初始化离线数据
		OfflineDataManager.initOfflineData();
		this.loadServices();
	},
	methods: {
		async loadServices() {
			if (this.loading) return;

			this.loading = true;

			try {
				// 直接使用离线数据，确保100%可用性
				const params = {
					page: this.page,
					pageSize: this.pageSize,
					keyword: this.searchKeyword,
					category: this.activeCategory === 'all' ? undefined : this.activeCategory
				};

				const result = OfflineDataManager.getOfflineServices(params);

				if (this.page === 1) {
					this.serviceList = result.data;
				} else {
					this.serviceList.push(...result.data);
				}

				this.hasMore = result.data.length === this.pageSize;
				this.loading = false;
				this.refreshing = false;

				// 显示成功提示
				if (this.page === 1 && result.data.length > 0) {
					FeedbackUtils.showSuccess(`已加载 ${result.data.length} 个服务`);
				}
			} catch (error) {
				this.loading = false;
				this.refreshing = false;
				FeedbackUtils.showError('数据加载失败，请重试');
				console.error('加载服务数据失败:', error);
			}
		},
		selectCategory(category) {
			this.activeCategory = category;
			this.page = 1;
			this.hasMore = true;
			this.loadServices();
		},
		searchService() {
			if (!this.searchKeyword.trim()) {
				uni.showToast({
					title: '请输入搜索关键词',
					icon: 'none'
				});
				return;
			}
			
			this.page = 1;
			this.hasMore = true;
			this.loadServices();
		},
		refreshData() {
			this.refreshing = true;
			this.page = 1;
			this.hasMore = true;
			this.loadServices();
		},
		loadMore() {
			if (this.hasMore && !this.loading) {
				this.page++;
				this.loadServices();
			}
		},
		viewDetail(item) {
			uni.navigateTo({
				url: `/pages/service/detail?id=${item.id}`
			});
		},
		contactProvider(item) {
			uni.makePhoneCall({
				phoneNumber: item.phone
			});
		},
		bookService(item) {
			uni.navigateTo({
				url: `/pages/service/book?id=${item.id}`
			});
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	height: 100vh;
	display: flex;
	flex-direction: column;
}

.search-section {
	background-color: #fff;
	padding: 20rpx;
	border-bottom: 1rpx solid #eee;
}

.search-bar {
	display: flex;
}

.search-input {
	flex: 1;
	height: 70rpx;
	padding: 0 20rpx;
	border: 1rpx solid #ddd;
	border-radius: 35rpx;
	font-size: 28rpx;
	margin-right: 20rpx;
}

.search-btn {
	width: 120rpx;
	height: 70rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: #fff;
	border: none;
	border-radius: 35rpx;
	font-size: 28rpx;
}

.category-section {
	background-color: #fff;
	border-bottom: 1rpx solid #eee;
}

.category-scroll {
	white-space: nowrap;
	padding: 20rpx;
}

.category-item {
	display: inline-flex;
	flex-direction: column;
	align-items: center;
	margin-right: 40rpx;
	padding: 15rpx 10rpx;
	border-radius: 15rpx;
}

.category-item.active {
	background: rgba(255, 138, 0, 0.1);
}

.category-icon-wrapper {
	width: 50rpx;
	height: 50rpx;
	margin-bottom: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.category-text {
	font-size: 24rpx;
	color: #666;
}

.category-item.active .category-text {
	color: #ff8a00;
	font-weight: bold;
}

.service-list {
	flex: 1;
	padding: 20rpx;
}

.service-item {
	background-color: #fff;
	border-radius: 20rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.service-image {
	width: 100%;
	height: 300rpx;
}

.service-content {
	padding: 30rpx;
}

.service-header {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
}

.service-name {
	flex: 1;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.service-rating {
	display: flex;
	align-items: center;
}

.rating-score {
	font-size: 28rpx;
	color: #ff9500;
	font-weight: bold;
}

.rating-text {
	font-size: 24rpx;
	color: #999;
	margin-left: 5rpx;
}

.service-desc {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	margin-bottom: 15rpx;
}

.service-provider {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.provider-label {
	font-size: 24rpx;
	color: #999;
}

.provider-name {
	font-size: 24rpx;
	color: #ff8a00;
	font-weight: bold;
}

.service-info {
	display: flex;
	justify-content: space-between;
	margin-bottom: 15rpx;
}

.service-coverage {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.coverage-label {
	font-size: 24rpx;
	color: #999;
}

.coverage-value {
	font-size: 24rpx;
	color: #666;
}

.info-item {
	display: flex;
	align-items: center;
}

.info-label {
	font-size: 24rpx;
	color: #999;
}

.info-value {
	font-size: 24rpx;
	color: #333;
}

.info-value.price {
	color: #ff8a00;
	font-weight: bold;
	font-size: 28rpx;
}

.info-unit {
	font-size: 22rpx;
	color: #999;
	margin-left: 2rpx;
}

.service-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 10rpx;
	margin-bottom: 20rpx;
}

.tag {
	padding: 8rpx 16rpx;
	background-color: rgba(255, 138, 0, 0.1);
	color: #ff8a00;
	font-size: 22rpx;
	border-radius: 15rpx;
}

.service-actions {
	display: flex;
	gap: 20rpx;
}

.action-btn {
	flex: 1;
	height: 70rpx;
	border-radius: 35rpx;
	font-size: 28rpx;
	border: 1rpx solid #ddd;
	background-color: #fff;
	color: #666;
}

.action-btn.primary {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: #fff;
	border-color: #ff8a00;
}

.load-more, .no-more {
	text-align: center;
	padding: 30rpx;
	font-size: 28rpx;
	color: #999;
}

.empty {
	text-align: center;
	padding: 100rpx 0;
}

.empty-icon {
	margin-bottom: 30rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}
</style>
