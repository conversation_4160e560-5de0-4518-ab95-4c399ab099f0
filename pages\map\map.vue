<template>
	<view class="map-page">
		<!-- 顶部搜索栏 -->
		<view class="top-search-bar">
			<view class="search-wrapper">
				<view class="search-input-container">
					<Icon name="search-line" size="32rpx" color="#999" class="search-icon"></Icon>
					<input
						class="search-input"
						placeholder="搜索附近养老机构"
						v-model="searchKeyword"
						@confirm="searchLocation"
						@input="onSearchInput"
					/>
					<view v-if="searchKeyword" class="clear-search-btn" @click="clearSearch">
						<Icon name="close-circle-fill" size="28rpx" color="#ccc"></Icon>
					</view>
				</view>

				<view class="search-action-btn" @click="searchLocation">
					<Icon name="search-line" size="28rpx" color="#fff"></Icon>
				</view>
			</view>
		</view>

		<!-- 快速分类栏 -->
		<view class="quick-category-bar">
			<scroll-view scroll-x="true" class="category-scroll" show-scrollbar="false">
				<view class="category-list">
					<view
						class="category-chip"
						:class="{ active: activeCategory === item.key }"
						v-for="(item, index) in categoryList"
						:key="index"
						@click="selectCategory(item.key)"
					>
						<Icon :name="item.icon" size="28rpx" :color="activeCategory === item.key ? '#fff' : '#ff8a00'"></Icon>
						<text class="category-text">{{item.name}}</text>
					</view>
				</view>
			</scroll-view>

			<view class="filter-trigger-btn" @click="toggleFilterPanel">
				<Icon name="filter-line" size="28rpx" color="#ff8a00"></Icon>
				<text class="filter-count" v-if="getActiveFilterCount() > 0">{{getActiveFilterCount()}}</text>
			</view>
		</view>



		<!-- 机构详情卡片 -->
		<view class="institution-detail-card" v-if="selectedMarker">
			<view class="detail-card-header">
				<view class="institution-info">
					<view class="institution-image-container">
						<image
							v-if="selectedMarker.image && !selectedMarker.imageError"
							:src="selectedMarker.image"
							class="institution-image"
							mode="aspectFill"
							@error="handleImageError(selectedMarker)"
						></image>
						<view v-else class="institution-icon-placeholder">
							<Icon :name="getCategoryIcon(selectedMarker.category)" size="48rpx" color="#fff"></Icon>
						</view>
					</view>

					<view class="institution-basic-info">
						<text class="institution-name">{{selectedMarker.title}}</text>
						<view class="institution-type-rating">
							<view class="type-badge">{{selectedMarker.type}}</view>
							<view class="rating-display">
								<Icon name="star-fill" size="24rpx" color="#ff8a00"></Icon>
								<text class="rating-text">{{selectedMarker.rating}}</text>
							</view>
						</view>
						<text class="institution-distance">距离 {{selectedMarker.distance}}</text>
					</view>
				</view>

				<view class="detail-close-btn" @click="closePanel">
					<Icon name="close-line" size="32rpx" color="#666"></Icon>
				</view>
			</view>

			<view class="detail-card-content">
				<view class="detail-info-row">
					<Icon name="location-line" size="28rpx" color="#ff8a00"></Icon>
					<text class="detail-info-text">{{selectedMarker.address}}</text>
				</view>
				<view class="detail-info-row">
					<Icon name="phone-line" size="28rpx" color="#ff8a00"></Icon>
					<text class="detail-info-text">{{selectedMarker.phone}}</text>
				</view>
				<view class="detail-info-row" v-if="selectedMarker.price">
					<Icon name="money-cny-circle-line" size="28rpx" color="#ff8a00"></Icon>
					<text class="detail-info-text">￥{{selectedMarker.price}}/月</text>
				</view>
				<view class="detail-info-row" v-if="selectedMarker.beds">
					<Icon name="hotel-bed-line" size="28rpx" color="#ff8a00"></Icon>
					<text class="detail-info-text">床位 {{selectedMarker.availableBeds}}/{{selectedMarker.beds}}</text>
				</view>
			</view>

			<view class="detail-card-actions">
				<InteractiveButton
					type="secondary"
					size="large"
					text="拨打电话"
					icon="phone-line"
					@click="callPhone"
				></InteractiveButton>
				<InteractiveButton
					type="primary"
					size="large"
					text="导航前往"
					icon="navigation-line"
					@click="navigateToLocation"
				></InteractiveButton>
				<InteractiveButton
					type="secondary"
					size="large"
					text="查看详情"
					icon="eye-line"
					@click="viewDetail"
				></InteractiveButton>
			</view>
		</view>

		<!-- 机构列表面板 -->
		<view class="institution-list-panel" v-if="!selectedMarker">
			<view class="list-panel-header">
				<view class="list-title-section">
					<text class="list-main-title">附近{{getCurrentCategoryName()}}</text>
					<view class="list-count-badge">{{filteredNearbyList.length}}</view>
				</view>
				<view class="list-sort-section">
					<view class="sort-btn" @click="showSortOptions = !showSortOptions">
						<Icon name="sort-desc" size="24rpx" color="#ff8a00"></Icon>
						<text class="sort-text">排序</text>
					</view>
				</view>
			</view>

			<scroll-view
				scroll-y="true"
				class="institution-list-scroll"
				enhanced="true"
				bounces="true"
				:scroll-with-animation="true"
				:enable-back-to-top="true"
			>
				<view
					class="institution-list-item"
					v-for="(item, index) in filteredNearbyList"
					:key="index"
					@click="selectLocation(item)"
				>
					<view class="list-item-image-section">
						<image
							v-if="item.image && !item.imageError"
							:src="item.image"
							class="list-item-image"
							mode="aspectFill"
							@error="handleImageError(item)"
						></image>
						<view v-else class="list-item-icon-placeholder">
							<Icon :name="getCategoryIcon(item.category)" size="48rpx" color="#fff"></Icon>
						</view>
						<view class="distance-badge">{{item.distance}}</view>
					</view>

					<view class="list-item-content-section">
						<view class="item-title-row">
							<text class="item-title">{{item.title}}</text>
							<view class="item-type-badge">{{item.type}}</view>
						</view>

						<view class="item-rating-row">
							<view class="rating-stars">
								<Icon name="star-fill" size="24rpx" color="#ff8a00"></Icon>
								<text class="rating-score">{{item.rating}}</text>
							</view>
							<text class="item-price" v-if="item.price">￥{{item.price}}/月</text>
						</view>

						<text class="item-address-text">{{item.address}}</text>

						<view class="item-features-row" v-if="item.beds">
							<view class="feature-item">
								<Icon name="hotel-bed-line" size="20rpx" color="#999"></Icon>
								<text class="feature-text">床位 {{item.availableBeds}}/{{item.beds}}</text>
							</view>
						</view>
					</view>

					<view class="list-item-action-section">
						<InteractiveButton
							type="secondary"
							size="small"
							text="电话"
							icon="phone-line"
							@click.stop="callPhone(item)"
						></InteractiveButton>
						<InteractiveButton
							type="primary"
							size="small"
							text="导航"
							icon="navigation-line"
							@click.stop="navigateToLocation(item)"
						></InteractiveButton>
					</view>
				</view>

				<!-- 列表底部提示 -->
				<view class="list-bottom-tip" v-if="filteredNearbyList.length === 0">
					<Icon name="search-line" size="48rpx" color="#ccc"></Icon>
					<text class="tip-text">暂无符合条件的机构</text>
					<text class="tip-subtext">请尝试调整筛选条件</text>
				</view>
			</scroll-view>
		</view>

		<!-- 高级筛选面板 -->
		<view v-if="showFilterPanel" class="advanced-filter-mask" @click="hideFilterPanel">
			<view class="advanced-filter-panel" @click.stop>
				<view class="filter-panel-header">
					<view class="filter-title-section">
						<Icon name="filter-line" size="32rpx" color="#ff8a00"></Icon>
						<text class="filter-main-title">筛选条件</text>
					</view>
					<view class="filter-close-btn" @click="hideFilterPanel">
						<Icon name="close-line" size="32rpx" color="#666"></Icon>
					</view>
				</view>

				<scroll-view scroll-y="true" class="filter-content-scroll">
					<!-- 机构类型筛选 -->
					<view class="filter-section">
						<view class="section-header">
							<Icon name="building-line" size="28rpx" color="#ff8a00"></Icon>
							<text class="section-title">机构类型</text>
						</view>
						<view class="filter-options-grid">
							<view
								v-for="(option, index) in typeOptions"
								:key="index"
								class="filter-option-chip"
								:class="{ active: selectedTypes.includes(option.value) }"
								@click="toggleType(option.value)"
							>
								<Icon :name="option.icon" size="24rpx" :color="selectedTypes.includes(option.value) ? '#fff' : '#ff8a00'"></Icon>
								<text class="option-chip-text">{{ option.label }}</text>
							</view>
						</view>
					</view>

					<!-- 距离范围筛选 -->
					<view class="filter-section">
						<view class="section-header">
							<Icon name="location-line" size="28rpx" color="#ff8a00"></Icon>
							<text class="section-title">距离范围</text>
						</view>
						<view class="filter-options-list">
							<view
								v-for="(distance, index) in distanceOptions"
								:key="index"
								class="filter-option-item"
								:class="{ active: selectedDistance === distance.value }"
								@click="selectDistance(distance.value)"
							>
								<view class="option-radio" :class="{ checked: selectedDistance === distance.value }">
									<Icon v-if="selectedDistance === distance.value" name="check-line" size="20rpx" color="#fff"></Icon>
								</view>
								<text class="option-item-text">{{ distance.label }}</text>
							</view>
						</view>
					</view>

					<!-- 评分要求筛选 -->
					<view class="filter-section">
						<view class="section-header">
							<Icon name="star-fill" size="28rpx" color="#ff8a00"></Icon>
							<text class="section-title">评分要求</text>
						</view>
						<view class="filter-options-list">
							<view
								v-for="(rating, index) in ratingOptions"
								:key="index"
								class="filter-option-item"
								:class="{ active: selectedRating === rating.value }"
								@click="selectRating(rating.value)"
							>
								<view class="option-radio" :class="{ checked: selectedRating === rating.value }">
									<Icon v-if="selectedRating === rating.value" name="check-line" size="20rpx" color="#fff"></Icon>
								</view>
								<text class="option-item-text">{{ rating.label }}</text>
							</view>
						</view>
					</view>

					<!-- 价格区间筛选 -->
					<view class="filter-section">
						<view class="section-header">
							<Icon name="money-cny-circle-line" size="28rpx" color="#ff8a00"></Icon>
							<text class="section-title">价格区间</text>
						</view>
						<view class="filter-options-list">
							<view
								v-for="(price, index) in priceOptions"
								:key="index"
								class="filter-option-item"
								:class="{ active: selectedPrice === price.value }"
								@click="selectPrice(price.value)"
							>
								<view class="option-radio" :class="{ checked: selectedPrice === price.value }">
									<Icon v-if="selectedPrice === price.value" name="check-line" size="20rpx" color="#fff"></Icon>
								</view>
								<text class="option-item-text">{{ price.label }}</text>
							</view>
						</view>
					</view>
				</scroll-view>

				<view class="filter-panel-actions">
					<InteractiveButton
						type="secondary"
						size="large"
						text="重置筛选"
						icon="refresh-line"
						@click="resetFilter"
					></InteractiveButton>
					<InteractiveButton
						type="primary"
						size="large"
						text="应用筛选"
						icon="check-line"
						@click="applyFilter"
					></InteractiveButton>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'
import FeedbackUtils from '@/utils/feedback.js'

export default {
	components: {
		Icon,
		InteractiveButton
	},
	data() {
		return {
			searchKeyword: '',
			activeCategory: 'institution',
			selectedMarker: null,

			// 筛选相关
			showFilterPanel: false,
			showSortOptions: false,
			selectedDistance: '',
			selectedRating: '',
			selectedPrice: '',
			selectedTypes: [], // 选中的机构类型（支持多选）

			categoryList: [
				{
					key: 'institution',
					name: '养老机构',
					icon: 'building-line'
				},
				{
					key: 'hospital',
					name: '医疗服务',
					icon: 'health-book-line'
				},
				{
					key: 'pharmacy',
					name: '药店',
					icon: 'medicine-bottle-line'
				},
				{
					key: 'emergency',
					name: '紧急服务',
					icon: 'alarm-warning-line'
				},
				{
					key: 'recreation',
					name: '休闲娱乐',
					icon: 'gamepad-line'
				}
			],

			// 筛选选项
			distanceOptions: [
				{ label: '不限', value: '' },
				{ label: '500米内', value: '500' },
				{ label: '1公里内', value: '1000' },
				{ label: '3公里内', value: '3000' },
				{ label: '5公里内', value: '5000' }
			],
			ratingOptions: [
				{ label: '不限', value: '' },
				{ label: '3分以上', value: '3' },
				{ label: '4分以上', value: '4' },
				{ label: '4.5分以上', value: '4.5' }
			],
			priceOptions: [
				{ label: '不限', value: '' },
				{ label: '2000以下', value: '2000' },
				{ label: '2000-5000', value: '2000-5000' },
				{ label: '5000-8000', value: '5000-8000' },
				{ label: '8000以上', value: '8000' }
			],
			typeOptions: [
				{ label: '养老院', value: '养老院', icon: 'building-line' },
				{ label: '护理院', value: '护理院', icon: 'nurse-line' },
				{ label: '老年公寓', value: '老年公寓', icon: 'home-heart-line' },
				{ label: '日间照料中心', value: '日间照料中心', icon: 'sun-line' },
				{ label: '社区服务站', value: '社区服务站', icon: 'community-line' }
			],

			markers: [],
			nearbyList: []
		}
	},
	computed: {
		// 筛选后的附近列表
		filteredNearbyList() {
			let list = this.nearbyList;

			// 机构类型筛选
			if (this.selectedTypes.length > 0) {
				list = list.filter(item => this.selectedTypes.includes(item.type));
			}

			// 距离筛选
			if (this.selectedDistance) {
				const maxDistance = parseInt(this.selectedDistance);
				list = list.filter(item => {
					const distance = parseInt(item.distance.replace(/[^\d]/g, ''));
					return distance <= maxDistance;
				});
			}

			// 评分筛选
			if (this.selectedRating) {
				const minRating = parseFloat(this.selectedRating);
				list = list.filter(item => item.rating >= minRating);
			}

			// 价格筛选
			if (this.selectedPrice) {
				if (this.selectedPrice.includes('-')) {
					const [min, max] = this.selectedPrice.split('-').map(p => parseInt(p));
					list = list.filter(item => {
						const price = item.price || 0;
						return price >= min && price <= max;
					});
				} else {
					const priceLimit = parseInt(this.selectedPrice);
					if (this.selectedPrice === '2000') {
						list = list.filter(item => (item.price || 0) <= priceLimit);
					} else if (this.selectedPrice === '8000') {
						list = list.filter(item => (item.price || 0) >= priceLimit);
					}
				}
			}

			return list;
		}
	},
	onLoad() {
		this.loadMarkers();
	},
	onShow() {
		this.refreshData();
	},
	methods: {

		loadMarkers() {
			// 模拟加载标记点数据 - 丰富的养老机构数据
			const mockData = [
				// 养老机构
				{
					id: 1,
					title: '阳光养老院',
					address: '北京市朝阳区阳光街123号',
					phone: '010-12345678',
					rating: 4.8,
					distance: '500m',
					category: 'institution',
					type: '养老院',
					price: 3500,
					beds: 120,
					availableBeds: 15,
					image: '/picture/nursing_home_1.jpg',
					imageError: false,
					features: ['24小时护理', '医疗服务', '康复理疗', '营养配餐'],
					description: '专业的养老护理机构，提供全方位的老年人照护服务，拥有专业的医护团队和完善的康复设施。',
					establishedYear: 2015,
					level: '三级',
					reviews: [
						{ user: '张女士', rating: 5, comment: '服务很好，护理人员很专业，老人住得很舒心。' },
						{ user: '李先生', rating: 4, comment: '环境不错，设施齐全，就是价格稍微贵一点。' }
					],
					facilities: ['无障碍设施', '康复训练室', '娱乐活动室', '医务室', '餐厅', '花园']
				},
				{
					id: 2,
					title: '康乐老年公寓',
					address: '北京市海淀区康乐路456号',
					phone: '010-87654321',
					rating: 4.6,
					distance: '800m',
					category: 'institution',
					type: '老年公寓',
					price: 4200,
					beds: 80,
					availableBeds: 8,
					image: '/picture/nursing_home_2.jpg',
					imageError: false,
					features: ['独立居住', '社区活动', '健康管理', '家政服务'],
					description: '现代化老年公寓，提供独立的居住空间和丰富的社区活动，让老年人享受自由而有保障的生活。',
					establishedYear: 2018,
					level: '二级',
					reviews: [
						{ user: '王阿姨', rating: 5, comment: '公寓很新，设施现代化，社区活动丰富多彩。' },
						{ user: '赵叔叔', rating: 4, comment: '居住环境很好，就是离市中心稍远一些。' }
					],
					facilities: ['健身房', '图书室', '棋牌室', '小超市', '理发店', '洗衣房']
				},
				{
					id: 3,
					latitude: this.latitude - 0.001,
					longitude: this.longitude + 0.003,
					title: '温馨护理中心',
					address: '北京市西城区温馨路789号',
					phone: '010-11223344',
					rating: 4.9,
					distance: '600m',
					category: 'institution',
					type: '护理院',
					price: 5800,
					beds: 60,
					availableBeds: 3,
					image: '/picture/nursing_home_3.jpg',
					imageError: false,
					iconPath: '/static/markers/institution.png',
					width: 30,
					height: 30
				},
				{
					id: 4,
					latitude: this.latitude - 0.003,
					longitude: this.longitude - 0.001,
					title: '幸福家园日间照料中心',
					address: '北京市东城区幸福街88号',
					phone: '010-55667788',
					rating: 4.4,
					distance: '1.2km',
					category: 'institution',
					type: '日间照料中心',
					price: 1800,
					beds: 40,
					availableBeds: 12,
					image: '/picture/nursing_home_4.jpg',
					imageError: false,
					iconPath: '/static/markers/institution.png',
					width: 30,
					height: 30
				},
				{
					id: 5,
					latitude: this.latitude + 0.002,
					longitude: this.longitude + 0.004,
					title: '爱心社区服务站',
					address: '北京市朝阳区爱心路66号',
					phone: '010-99887766',
					rating: 4.2,
					distance: '1.5km',
					category: 'institution',
					type: '社区服务站',
					price: 800,
					beds: 20,
					availableBeds: 6,
					image: '/picture/nursing_home_5.jpg',
					imageError: false,
					iconPath: '/static/markers/institution.png',
					width: 30,
					height: 30
				},
				{
					id: 6,
					latitude: this.latitude - 0.004,
					longitude: this.longitude + 0.001,
					title: '金秋养老公寓',
					address: '北京市丰台区金秋大道168号',
					phone: '010-33445566',
					rating: 4.7,
					distance: '2.1km',
					category: 'institution',
					type: '老年公寓',
					price: 4800,
					beds: 150,
					availableBeds: 22,
					image: '/picture/b3bc07f949264b36811e26cf01c7f50c.jpeg',
					imageError: false,
					iconPath: '/static/markers/institution.png',
					width: 30,
					height: 30
				},
				{
					id: 7,
					latitude: this.latitude + 0.005,
					longitude: this.longitude - 0.003,
					title: '安康护理院',
					address: '北京市石景山区安康街99号',
					phone: '010-77889900',
					rating: 4.5,
					distance: '2.8km',
					category: 'institution',
					type: '护理院',
					price: 6200,
					beds: 90,
					availableBeds: 5,
					image: '/picture/659c362e1e774324870c7a9200adc1e7.jpeg',
					imageError: false,
					iconPath: '/static/markers/institution.png',
					width: 30,
					height: 30
				},
				{
					id: 8,
					latitude: this.latitude - 0.002,
					longitude: this.longitude - 0.004,
					title: '夕阳红养老院',
					address: '北京市通州区夕阳路188号',
					phone: '010-22334455',
					rating: 4.3,
					distance: '3.2km',
					category: 'institution',
					type: '养老院',
					price: 3200,
					beds: 100,
					availableBeds: 18,
					image: '/picture/v2-cf8da5bb3e3a5a87f1ce7f498397db9d_720w.jpg',
					imageError: false,
					iconPath: '/static/markers/institution.png',
					width: 30,
					height: 30
				},
				{
					id: 9,
					latitude: this.latitude + 0.004,
					longitude: this.longitude + 0.002,
					title: '康宁日间照料中心',
					address: '北京市昌平区康宁街77号',
					phone: '010-66778899',
					rating: 4.1,
					distance: '3.8km',
					category: 'institution',
					type: '日间照料中心',
					price: 2200,
					beds: 35,
					availableBeds: 9,
					image: '/picture/R-C.jpg',
					imageError: false,
					iconPath: '/static/markers/institution.png',
					width: 30,
					height: 30
				},
				{
					id: 10,
					latitude: this.latitude - 0.005,
					longitude: this.longitude - 0.002,
					title: '福寿康养老公寓',
					address: '北京市大兴区福寿路288号',
					phone: '010-44556677',
					rating: 4.6,
					distance: '4.2km',
					category: 'institution',
					type: '老年公寓',
					price: 5200,
					beds: 180,
					availableBeds: 28,
					image: '/picture/R-C (1).jpg',
					imageError: false,
					iconPath: '/static/markers/institution.png',
					width: 30,
					height: 30
				},

				// 医疗服务
				{
					id: 11,
					latitude: this.latitude - 0.002,
					longitude: this.longitude + 0.002,
					title: '社区医院',
					address: '北京市朝阳区健康街道123号',
					phone: '010-87654321',
					rating: 4.5,
					distance: '800m',
					category: 'hospital',
					type: '社区医院',
					image: '/picture/20226131655111829696_10006313.jpg',
					imageError: false,
					iconPath: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAiIGhlaWdodD0iMzAiIHZpZXdCb3g9IjAgMCAzMCAzMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTUiIGN5PSIxNSIgcj0iMTUiIGZpbGw9IiM0ZWNkYzQiLz4KPHN2ZyB4PSI3IiB5PSI3IiB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDZWMThNNiAxMkgxOCIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cjwvc3ZnPg==',
					width: 30,
					height: 30
				},
				{
					id: 12,
					latitude: this.latitude + 0.003,
					longitude: this.longitude + 0.003,
					title: '康复医疗中心',
					address: '北京市海淀区康复大道456号',
					phone: '010-12348765',
					rating: 4.7,
					distance: '1.5km',
					category: 'hospital',
					type: '康复中心',
					image: '/picture/FAB64913B02FEDD318336D49F0A550A1_w798h530.png',
					imageError: false,
					iconPath: '/static/markers/hospital.png',
					width: 30,
					height: 30
				},

				// 新增更多样化的养老机构
				{
					id: 13,
					latitude: this.latitude + 0.006,
					longitude: this.longitude - 0.004,
					title: '银龄智慧养老中心',
					address: '北京市西城区银龄大街288号',
					phone: '010-88776655',
					rating: 4.9,
					distance: '5.2km',
					category: 'institution',
					type: '智慧养老院',
					price: 7800,
					beds: 200,
					availableBeds: 35,
					image: '/picture/nursing_home_1.jpg',
					imageError: false,
					iconPath: '/static/markers/institution.png',
					width: 30,
					height: 30,
					features: ['智能监护', 'AI健康管理', '远程医疗', '智能家居', '24小时护理'],
					description: '采用最新智能化技术的现代养老中心，配备AI健康监测系统、智能护理机器人，为老年人提供科技化的贴心服务。',
					establishedYear: 2022,
					level: '四级',
					reviews: [
						{ user: '李女士', rating: 5, comment: '科技感很强，设备先进，护理很专业，老人很喜欢。' },
						{ user: '陈先生', rating: 5, comment: '智能化程度很高，家属可以远程了解老人状况，很放心。' }
					],
					facilities: ['智能监护室', 'VR娱乐室', '远程医疗室', '智能餐厅', '数字图书馆', '康复机器人室']
				},
				{
					id: 14,
					latitude: this.latitude - 0.006,
					longitude: this.longitude + 0.005,
					title: '绿野田园养老村',
					address: '北京市房山区绿野路168号',
					phone: '010-55443322',
					rating: 4.4,
					distance: '8.5km',
					category: 'institution',
					type: '田园养老村',
					price: 2800,
					beds: 150,
					availableBeds: 45,
					image: '/picture/nursing_home_2.jpg',
					imageError: false,
					iconPath: '/static/markers/institution.png',
					width: 30,
					height: 30,
					features: ['田园生活', '有机蔬菜', '宠物陪伴', '农耕体验', '自然疗法'],
					description: '位于郊区的田园式养老社区，环境优美，空气清新，提供农耕体验和自然疗法，让老年人享受田园生活的乐趣。',
					establishedYear: 2019,
					level: '二级',
					reviews: [
						{ user: '王奶奶', rating: 4, comment: '环境很好，空气清新，就是离市区有点远。' },
						{ user: '张爷爷', rating: 5, comment: '可以种菜养花，生活很充实，价格也实惠。' }
					],
					facilities: ['有机菜园', '花卉温室', '宠物乐园', '垂钓池', '步行小径', '观景亭']
				},
				{
					id: 15,
					latitude: this.latitude + 0.002,
					longitude: this.longitude - 0.006,
					title: '颐和高端养老会所',
					address: '北京市海淀区颐和园路99号',
					phone: '010-99887766',
					rating: 4.8,
					distance: '3.8km',
					category: 'institution',
					type: '高端养老会所',
					price: 12800,
					beds: 80,
					availableBeds: 8,
					image: '/picture/nursing_home_3.jpg',
					imageError: false,
					iconPath: '/static/markers/institution.png',
					width: 30,
					height: 30,
					features: ['私人管家', '高端医疗', '营养师配餐', '专属司机', '文化沙龙'],
					description: '顶级养老会所，提供一对一私人管家服务，配备专业医疗团队和营养师，为高端客户提供尊贵的养老体验。',
					establishedYear: 2020,
					level: '五级',
					reviews: [
						{ user: '刘女士', rating: 5, comment: '服务非常到位，环境优雅，医疗条件一流。' },
						{ user: '周先生', rating: 4, comment: '价格虽高但物有所值，父亲住得很满意。' }
					],
					facilities: ['私人套房', '高端医疗中心', '营养配餐室', '文化沙龙', '私人花园', '豪华SPA']
				},

				// 药店服务
				{
					id: 16,
					latitude: this.latitude + 0.001,
					longitude: this.longitude - 0.001,
					title: '康民大药房',
					address: '北京市朝阳区健康街45号',
					phone: '010-66778899',
					rating: 4.5,
					distance: '300m',
					category: 'pharmacy',
					type: '连锁药店',
					image: '/picture/20226131655111829696_10006313.jpg',
					imageError: false,
					iconPath: '/static/markers/pharmacy.png',
					width: 30,
					height: 30,
					features: ['24小时营业', '送药上门', '健康咨询', '血压测量'],
					description: '专业连锁药店，提供各类药品和健康产品，24小时营业，支持送药上门服务。',
					services: ['处方药', '非处方药', '保健品', '医疗器械', '健康检测']
				},
				{
					id: 17,
					latitude: this.latitude - 0.002,
					longitude: this.longitude + 0.001,
					title: '同仁堂中医药店',
					address: '北京市东城区中医街88号',
					phone: '010-77889900',
					rating: 4.7,
					distance: '1.2km',
					category: 'pharmacy',
					type: '中医药店',
					image: '/picture/FAB64913B02FEDD318336D49F0A550A1_w798h530.png',
					imageError: false,
					iconPath: '/static/markers/pharmacy.png',
					width: 30,
					height: 30,
					features: ['中药配方', '名医坐诊', '养生咨询', '膏方定制'],
					description: '百年老字号中医药店，提供传统中药配方和现代中医服务，名老中医定期坐诊。',
					services: ['中药饮片', '中成药', '养生茶', '膏方', '中医咨询']
				},

				// 紧急服务
				{
					id: 18,
					latitude: this.latitude + 0.002,
					longitude: this.longitude + 0.002,
					title: '朝阳区急救中心',
					address: '北京市朝阳区急救路120号',
					phone: '120',
					rating: 4.6,
					distance: '1.8km',
					category: 'emergency',
					type: '急救中心',
					image: '/picture/nursing_home_4.jpg',
					imageError: false,
					iconPath: '/static/markers/emergency.png',
					width: 30,
					height: 30,
					features: ['24小时急救', '专业救护车', '急诊科', '重症监护'],
					description: '专业急救医疗中心，24小时提供急救服务，配备先进的急救设备和专业医护团队。',
					services: ['急救出诊', '急诊治疗', '重症监护', '医疗转运', '健康体检']
				},

				// 休闲娱乐
				{
					id: 19,
					latitude: this.latitude - 0.003,
					longitude: this.longitude - 0.002,
					title: '夕阳红老年活动中心',
					address: '北京市西城区文化路66号',
					phone: '010-55667788',
					rating: 4.3,
					distance: '2.5km',
					category: 'recreation',
					type: '老年活动中心',
					image: '/picture/nursing_home_5.jpg',
					imageError: false,
					iconPath: '/static/markers/recreation.png',
					width: 30,
					height: 30,
					features: ['棋牌娱乐', '书法绘画', '太极拳', '合唱团', '健康讲座'],
					description: '专为老年人设计的综合活动中心，提供丰富多彩的文体活动和社交平台。',
					services: ['棋牌室', '书画室', '舞蹈室', '阅览室', '健身房', '茶艺室']
				}
			];

			this.nearbyList = mockData.filter(item => item.category === this.activeCategory);
		},
		selectCategory(category) {
			FeedbackUtils.lightFeedback();
			this.activeCategory = category;
			this.selectedMarker = null;
			this.loadMarkers();
		},
		searchLocation() {
			if (!this.searchKeyword.trim()) {
				uni.showToast({
					title: '请输入搜索关键词',
					icon: 'none'
				});
				return;
			}
			
			// 实现搜索逻辑
			console.log('搜索:', this.searchKeyword);
			uni.showToast({
				title: '搜索功能开发中',
				icon: 'none'
			});
		},

		selectLocation(item) {
			this.selectedMarker = item;
		},
		closePanel() {
			this.selectedMarker = null;
		},
		callPhone(item = this.selectedMarker) {
			if (item && item.phone) {
				uni.makePhoneCall({
					phoneNumber: item.phone
				});
			}
		},
		navigateToLocation(item = this.selectedMarker) {
			if (item) {
				uni.openLocation({
					latitude: item.latitude,
					longitude: item.longitude,
					name: item.title,
					address: item.address
				});
			}
		},
		viewDetail() {
			if (this.selectedMarker) {
				const category = this.selectedMarker.category;
				let url = '';

				if (category === 'institution') {
					url = `/pages/institution/detail?id=${this.selectedMarker.id}`;
				} else if (category === 'hospital') {
					// 医院详情页面不存在，跳转到机构详情页面
					url = `/pages/institution/detail?id=${this.selectedMarker.id}`;
				}

				if (url) {
					uni.navigateTo({
						url,
						fail: (err) => {
							console.error('跳转失败:', err);
							uni.showToast({
								title: '页面跳转失败',
								icon: 'none'
							});
						}
					});
				}
			}
		},
		getCategoryName(key) {
			const category = this.categoryList.find(item => item.key === key);
			return category ? category.name : '';
		},
		refreshData() {
			this.loadMarkers();
		},

		// 搜索输入处理
		onSearchInput() {
			// 实时搜索功能可以在这里实现
		},

		// 清空搜索
		clearSearch() {
			FeedbackUtils.lightFeedback();
			this.searchKeyword = '';
		},

		// 切换筛选面板
		toggleFilterPanel() {
			FeedbackUtils.lightFeedback();
			this.showFilterPanel = !this.showFilterPanel;
		},

		// 隐藏筛选面板
		hideFilterPanel() {
			FeedbackUtils.lightFeedback();
			this.showFilterPanel = false;
		},

		// 获取激活的筛选条件数量
		getActiveFilterCount() {
			let count = 0;
			if (this.selectedTypes.length > 0) count++;
			if (this.selectedDistance) count++;
			if (this.selectedRating) count++;
			if (this.selectedPrice) count++;
			return count;
		},



		// 选择距离
		selectDistance(distance) {
			FeedbackUtils.lightFeedback();
			this.selectedDistance = distance;
		},

		// 选择评分
		selectRating(rating) {
			FeedbackUtils.lightFeedback();
			this.selectedRating = rating;
		},

		// 选择价格
		selectPrice(price) {
			FeedbackUtils.lightFeedback();
			this.selectedPrice = price;
		},

		// 机构类型多选切换
		toggleType(type) {
			FeedbackUtils.lightFeedback();
			const index = this.selectedTypes.indexOf(type);
			if (index > -1) {
				this.selectedTypes.splice(index, 1);
			} else {
				this.selectedTypes.push(type);
			}
		},

		// 重置筛选
		resetFilter() {
			FeedbackUtils.lightFeedback();
			this.selectedTypes = [];
			this.selectedDistance = '';
			this.selectedRating = '';
			this.selectedPrice = '';
			FeedbackUtils.showInfo('筛选条件已重置');
		},

		// 应用筛选
		applyFilter() {
			FeedbackUtils.lightFeedback();
			this.hideFilterPanel();

			// 显示筛选结果提示
			const resultCount = this.filteredNearbyList.length;
			FeedbackUtils.showSuccess(`找到 ${resultCount} 个符合条件的机构`);
		},

		// 获取分类图标
		getCategoryIcon(category) {
			const iconMap = {
				'institution': 'building-line',
				'hospital': 'health-book-line',
				'pharmacy': 'medicine-bottle-line',
				'emergency': 'alarm-warning-line',
				'recreation': 'gamepad-line'
			};
			return iconMap[category] || 'location-line';
		},

		// 处理图片加载错误
		handleImageError(item) {
			item.imageError = true;
		},

		// 获取选中类型文本
		getSelectedTypesText() {
			if (this.selectedTypes.length === 0) return '全部类型';
			if (this.selectedTypes.length === 1) return this.selectedTypes[0];
			return `${this.selectedTypes.length}个类型`;
		},

		// 获取选中距离文本
		getSelectedDistanceText() {
			if (!this.selectedDistance) return '不限距离';
			const option = this.distanceOptions.find(item => item.value === this.selectedDistance);
			return option ? option.label : '不限距离';
		},

		// 获取选中评分文本
		getSelectedRatingText() {
			if (!this.selectedRating) return '不限评分';
			const option = this.ratingOptions.find(item => item.value === this.selectedRating);
			return option ? option.label : '不限评分';
		},

		// 获取当前分类名称
		getCurrentCategoryName() {
			const category = this.categoryList.find(item => item.key === this.activeCategory);
			return category ? category.name : '养老机构';
		},

		// 获取当前分类图标
		getCurrentCategoryIcon() {
			const category = this.categoryList.find(item => item.key === this.activeCategory);
			return category ? category.icon : 'building-line';
		},

		// 选择价格区间
		selectPrice(price) {
			FeedbackUtils.lightFeedback();
			this.selectedPrice = this.selectedPrice === price ? '' : price;
		}
	}
}
</script>

<style scoped>
/* 页面整体布局 */
.map-page {
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	background: #f8f9fa;
}

/* 顶部搜索栏 */
.top-search-bar {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	padding: 20rpx 30rpx 25rpx;
	box-shadow: 0 4rpx 12rpx rgba(255, 138, 0, 0.2);
}

.search-wrapper {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.search-input-container {
	flex: 1;
	position: relative;
	display: flex;
	align-items: center;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 30rpx;
	height: 80rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.search-icon {
	position: absolute;
	left: 25rpx;
	z-index: 1;
}

.search-input {
	flex: 1;
	height: 100%;
	padding: 0 60rpx 0 60rpx;
	border: none;
	border-radius: 30rpx;
	font-size: 28rpx;
	background: transparent;
	color: #333;
}

.search-input::placeholder {
	color: #999;
	font-size: 28rpx;
}

.clear-search-btn {
	position: absolute;
	right: 25rpx;
	padding: 10rpx;
}

.search-action-btn {
	width: 80rpx;
	height: 80rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 25rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 2rpx solid rgba(255, 255, 255, 0.3);
}

/* 快速分类栏 */
.quick-category-bar {
	background: white;
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.category-scroll {
	flex: 1;
	white-space: nowrap;
}

.category-list {
	display: flex;
	padding: 0 30rpx;
	gap: 20rpx;
}

.category-chip {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 18rpx 25rpx;
	border-radius: 25rpx;
	border: 2rpx solid #ff8a00;
	background: white;
	transition: all 0.3s ease;
	white-space: nowrap;
	min-width: fit-content;
}

.category-chip.active {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	box-shadow: 0 4rpx 12rpx rgba(255, 138, 0, 0.3);
}

.category-text {
	font-size: 26rpx;
	color: #ff8a00;
	font-weight: 500;
}

.category-chip.active .category-text {
	color: white;
	font-weight: 600;
}

.filter-trigger-btn {
	position: relative;
	margin-right: 30rpx;
	padding: 18rpx 25rpx;
	background: #f8f9fa;
	border-radius: 25rpx;
	border: 2rpx solid #e9ecef;
}

.filter-count {
	position: absolute;
	top: -8rpx;
	right: -8rpx;
	background: #ff4757;
	color: white;
	font-size: 20rpx;
	padding: 4rpx 8rpx;
	border-radius: 50%;
	min-width: 32rpx;
	text-align: center;
	line-height: 1;
}



/* 机构详情卡片 */
.institution-detail-card {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	border-radius: 30rpx 30rpx 0 0;
	box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
	z-index: 200;
	animation: slideUp 0.3s ease-out;
}

.detail-card-header {
	display: flex;
	align-items: flex-start;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.institution-info {
	flex: 1;
	display: flex;
	gap: 20rpx;
}

.institution-image-container {
	width: 120rpx;
	height: 120rpx;
	border-radius: 20rpx;
	overflow: hidden;
	background: #f8f9fa;
}

.institution-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.institution-icon-placeholder {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
}

.institution-basic-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.institution-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	line-height: 1.2;
}

.institution-type-rating {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.type-badge {
	background: rgba(255, 138, 0, 0.1);
	color: #ff8a00;
	font-size: 22rpx;
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
	font-weight: 500;
}

.rating-display {
	display: flex;
	align-items: center;
	gap: 6rpx;
}

.rating-text {
	font-size: 26rpx;
	font-weight: 600;
	color: #ff8a00;
}

.institution-distance {
	font-size: 24rpx;
	color: #666;
}

.detail-close-btn {
	padding: 15rpx;
	background: #f8f9fa;
	border-radius: 50%;
}

.detail-card-content {
	padding: 25rpx 30rpx;
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.detail-info-row {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.detail-info-text {
	font-size: 28rpx;
	color: #333;
	line-height: 1.4;
}

.detail-card-actions {
	display: flex;
	gap: 15rpx;
	padding: 25rpx 30rpx 40rpx;
}

/* 机构列表面板 */
.institution-list-panel {
	flex: 1;
	background: white;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	margin-top: 0;
}

.list-panel-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 25rpx 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	border-radius: 0;
}

.list-title-section {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.list-main-title {
	font-size: 32rpx;
	font-weight: bold;
	color: white;
}

.list-count-badge {
	background: rgba(255, 255, 255, 0.2);
	color: white;
	font-size: 24rpx;
	padding: 8rpx 15rpx;
	border-radius: 15rpx;
	font-weight: 600;
}

.list-sort-section {
	display: flex;
	align-items: center;
}

.sort-btn {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 12rpx 20rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 20rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.sort-text {
	font-size: 24rpx;
	color: white;
	font-weight: 500;
}

.institution-list-scroll {
	flex: 1;
	padding: 0 30rpx 40rpx;
	background: #f8f9fa;
	min-height: 0;
	overflow-y: auto;
}

.institution-list-item {
	display: flex;
	padding: 25rpx 20rpx;
	margin: 15rpx 0;
	background: white;
	border-radius: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
	gap: 20rpx;
	transition: all 0.3s ease;
}

.institution-list-item:active {
	background: rgba(255, 138, 0, 0.05);
	transform: scale(0.98);
}

.list-item-image-section {
	position: relative;
	flex-shrink: 0;
}

.list-item-image {
	width: 140rpx;
	height: 140rpx;
	border-radius: 20rpx;
	object-fit: cover;
}

.list-item-icon-placeholder {
	width: 140rpx;
	height: 140rpx;
	border-radius: 20rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	display: flex;
	align-items: center;
	justify-content: center;
}

.distance-badge {
	position: absolute;
	top: 10rpx;
	right: 10rpx;
	background: rgba(0, 0, 0, 0.7);
	color: white;
	font-size: 20rpx;
	padding: 6rpx 10rpx;
	border-radius: 10rpx;
	font-weight: 500;
}

.list-item-content-section {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.item-title-row {
	display: flex;
	align-items: flex-start;
	gap: 15rpx;
}

.item-title {
	flex: 1;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	line-height: 1.3;
}

.item-type-badge {
	background: rgba(255, 138, 0, 0.1);
	color: #ff8a00;
	font-size: 22rpx;
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
	font-weight: 500;
	white-space: nowrap;
}

.item-rating-row {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.rating-stars {
	display: flex;
	align-items: center;
	gap: 6rpx;
}

.rating-score {
	font-size: 26rpx;
	font-weight: 600;
	color: #ff8a00;
}

.item-price {
	font-size: 26rpx;
	color: #ff8a00;
	font-weight: 600;
}

.item-address-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.4;
}

.item-features-row {
	display: flex;
	gap: 20rpx;
}

.feature-item {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.feature-text {
	font-size: 24rpx;
	color: #666;
}

.list-item-action-section {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
	justify-content: center;
	align-items: center;
}

.list-bottom-tip {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 80rpx 40rpx;
	gap: 20rpx;
}

.tip-text {
	font-size: 28rpx;
	color: #999;
	font-weight: 500;
}

.tip-subtext {
	font-size: 24rpx;
	color: #ccc;
}

.mini-btn {
	width: 120rpx;
	height: 60rpx;
	font-size: 24rpx;
	border-radius: 15rpx;
	border: 1rpx solid #e0e0e0;
	background-color: white;
	color: #666;
	font-weight: 500;
}

.mini-btn.primary {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border-color: #ff8a00;
}

/* 地图控制按钮组 */
.map-controls {
	position: absolute;
	right: 20rpx;
	bottom: 20rpx;
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.control-btn {
	width: 80rpx;
	height: 80rpx;
	background-color: #fff;
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.btn-icon {
	font-size: 32rpx;
}

/* 图标占位符 */
.item-image-container {
	flex-shrink: 0;
	width: 120rpx;
	height: 120rpx;
	margin-right: 20rpx;
}

.item-icon-placeholder {
	width: 100%;
	height: 100%;
	border-radius: 10rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 筛选面板 */
.filter-panel {
	background: white;
	border-radius: 30rpx 30rpx 0 0;
	max-height: 80vh;
	overflow-y: auto;
}

.filter-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.filter-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.filter-close {
	padding: 10rpx;
}

.filter-content {
	padding: 30rpx;
}

.filter-section {
	margin-bottom: 40rpx;
}

.section-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.filter-options {
	display: flex;
	flex-wrap: wrap;
	gap: 15rpx;
}

.filter-option {
	padding: 15rpx 25rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 25rpx;
	background: white;
	transition: all 0.3s ease;
}

.filter-option.active {
	border-color: #ff8a00;
	background: rgba(255, 138, 0, 0.1);
}

.option-text {
	font-size: 26rpx;
	color: #666;
}

.filter-option.active .option-text {
	color: #ff8a00;
	font-weight: 500;
}

/* 机构类型筛选特殊样式 */
.type-option {
	display: flex;
	align-items: center;
	gap: 10rpx;
	min-width: 140rpx;
	justify-content: space-between;
}

.type-option .option-text {
	flex: 1;
	text-align: center;
}

.filter-actions {
	display: flex;
	gap: 20rpx;
	padding: 30rpx;
	border-top: 1rpx solid #f0f0f0;
}

/* 搜索栏优化 */
.search-container {
	flex: 1;
	position: relative;
	display: flex;
	align-items: center;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 20rpx;
	margin-right: 20rpx;
}

.search-icon {
	position: absolute;
	left: 20rpx;
	z-index: 1;
}

.search-input {
	flex: 1;
	height: 80rpx;
	padding: 0 60rpx 0 60rpx;
	border: none;
	border-radius: 20rpx;
	font-size: 28rpx;
	background: transparent;
}

.clear-btn {
	position: absolute;
	right: 20rpx;
	padding: 10rpx;
}

.search-button {
	flex-shrink: 0;
}

/* 高级筛选面板 */
.advanced-filter-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 9999;
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 40rpx;
}

.advanced-filter-panel {
	background: white;
	border-radius: 25rpx;
	width: 100%;
	max-width: 700rpx;
	max-height: 85vh;
	overflow: hidden;
	animation: slideDown 0.3s ease-out;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.2);
}

.filter-panel-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
}

.filter-title-section {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.filter-main-title {
	font-size: 32rpx;
	font-weight: bold;
	color: white;
}

.filter-close-btn {
	padding: 15rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 50%;
}

.filter-content-scroll {
	max-height: 60vh;
	overflow-y: auto;
	padding: 30rpx;
}

.filter-section {
	margin-bottom: 40rpx;
}

.filter-section:last-child {
	margin-bottom: 0;
}

.section-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 25rpx;
}

.section-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

.filter-options-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 15rpx;
}

.filter-option-chip {
	display: flex;
	align-items: center;
	gap: 10rpx;
	padding: 18rpx 25rpx;
	border: 2rpx solid #ff8a00;
	border-radius: 25rpx;
	background: white;
	transition: all 0.3s ease;
}

.filter-option-chip.active {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	box-shadow: 0 4rpx 12rpx rgba(255, 138, 0, 0.3);
}

.option-chip-text {
	font-size: 26rpx;
	color: #ff8a00;
	font-weight: 500;
}

.filter-option-chip.active .option-chip-text {
	color: white;
	font-weight: 600;
}

.filter-options-list {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.filter-option-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 20rpx 25rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.filter-option-item.active {
	background: rgba(255, 138, 0, 0.1);
	border-color: #ff8a00;
}

.option-radio {
	width: 40rpx;
	height: 40rpx;
	border: 3rpx solid #ddd;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.option-radio.checked {
	border-color: #ff8a00;
	background: #ff8a00;
}

.option-item-text {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.filter-option-item.active .option-item-text {
	color: #ff8a00;
	font-weight: 600;
}

.filter-panel-actions {
	display: flex;
	gap: 20rpx;
	padding: 30rpx;
	border-top: 1rpx solid #f0f0f0;
	background: #f8f9fa;
}

.selector-panel {
	background: #f8f8f8;
	border-radius: 15rpx;
	margin-top: 15rpx;
	padding: 15rpx;
	max-height: 300rpx;
	overflow-y: auto;
}

.selector-option {
	display: flex;
	align-items: center;
	gap: 15rpx;
	padding: 20rpx 15rpx;
	border-radius: 10rpx;
	margin-bottom: 10rpx;
	background: white;
	transition: all 0.3s ease;
}

.selector-option.active {
	background: rgba(255, 138, 0, 0.1);
	border: 1rpx solid #ff8a00;
}

.option-text {
	flex: 1;
	font-size: 26rpx;
	color: #333;
}

.selector-option.active .option-text {
	color: #ff8a00;
	font-weight: 500;
}

.filter-actions {
	display: flex;
	gap: 20rpx;
	padding: 30rpx;
	border-top: 1rpx solid #f0f0f0;
}
</style>
