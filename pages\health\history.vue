<template>
	<view class="container">
		<PageHeader title="健康历史" showBack></PageHeader>
		
		<view class="content">
			<!-- 筛选器 -->
			<view class="filter-section">
				<view class="filter-tabs">
					<text 
						class="filter-tab" 
						:class="{ active: activeType === type.key }"
						v-for="type in healthTypes" 
						:key="type.key"
						@click="selectType(type.key)"
					>
						{{ type.name }}
					</text>
				</view>
				<view class="time-filter">
					<picker 
						:value="timeIndex" 
						:range="timeOptions" 
						range-key="label"
						@change="onTimeChange"
					>
						<view class="picker-display">
							<text class="picker-text">{{ timeOptions[timeIndex].label }}</text>
							<Icon name="arrow-down-s-line" size="20rpx" color="#999"></Icon>
						</view>
					</picker>
				</view>
			</view>

			<!-- 统计概览 -->
			<view class="stats-section">
				<view class="stats-card">
					<view class="stat-item">
						<text class="stat-value">{{ stats.total }}</text>
						<text class="stat-label">总记录</text>
					</view>
					<view class="stat-item">
						<text class="stat-value">{{ stats.normal }}</text>
						<text class="stat-label">正常</text>
					</view>
					<view class="stat-item">
						<text class="stat-value">{{ stats.warning }}</text>
						<text class="stat-label">偏高</text>
					</view>
					<view class="stat-item">
						<text class="stat-value">{{ stats.abnormal }}</text>
						<text class="stat-label">异常</text>
					</view>
				</view>
			</view>

			<!-- 历史记录列表 -->
			<view class="history-section">
				<view class="section-header">
					<text class="section-title">历史记录</text>
					<view class="sort-options">
						<text 
							class="sort-item" 
							:class="{ active: sortBy === sort.key }"
							v-for="sort in sortOptions" 
							:key="sort.key"
							@click="selectSort(sort.key)"
						>
							{{ sort.name }}
						</text>
					</view>
				</view>
				
				<scroll-view 
					class="history-list" 
					scroll-y="true"
					@scrolltolower="loadMore"
					refresher-enabled="true"
					@refresherrefresh="refreshData"
					:refresher-triggered="refreshing"
				>
					<view class="history-item" v-for="record in historyRecords" :key="record.id" @click="viewDetail(record)">
						<view class="record-date">
							<text class="date-day">{{ record.day }}</text>
							<text class="date-month">{{ record.month }}</text>
							<text class="date-time">{{ record.time }}</text>
						</view>
						<view class="record-content">
							<view class="record-header">
								<text class="record-type">{{ record.type }}</text>
								<view class="record-status" :class="record.status">
									<text class="status-text">{{ getStatusText(record.status) }}</text>
								</view>
							</view>
							<text class="record-value">{{ record.value }}</text>
							<text class="record-note" v-if="record.note">{{ record.note }}</text>
						</view>
						<view class="record-actions">
							<button class="action-btn" @click.stop="editRecord(record)">
								<Icon name="edit-line" size="20rpx" color="#ff8a00"></Icon>
							</button>
							<button class="action-btn" @click.stop="deleteRecord(record)">
								<Icon name="delete-line" size="20rpx" color="#f44336"></Icon>
							</button>
						</view>
					</view>
					
					<view class="load-more" v-if="hasMore">
						<text class="load-text">加载更多...</text>
					</view>
					
					<view class="no-more" v-else-if="historyRecords.length > 0">
						<text class="no-more-text">没有更多记录了</text>
					</view>
					
					<view class="empty-state" v-else>
						<Icon name="file-line" size="80rpx" color="#ddd"></Icon>
						<text class="empty-text">暂无健康记录</text>
						<InteractiveButton 
							type="primary" 
							text="添加记录" 
							size="small"
							@click="addRecord"
						/>
					</view>
				</scroll-view>
			</view>
		</view>

		<!-- 浮动添加按钮 -->
		<view class="fab" @click="addRecord">
			<Icon name="add-line" size="32rpx" color="white"></Icon>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'

export default {
	name: 'HealthHistory',
	components: {
		Icon,
		PageHeader,
		InteractiveButton
	},
	data() {
		return {
			activeType: 'all',
			timeIndex: 0,
			sortBy: 'time',
			refreshing: false,
			hasMore: true,
			page: 1,
			healthTypes: [
				{ key: 'all', name: '全部' },
				{ key: '血压', name: '血压' },
				{ key: '血糖', name: '血糖' },
				{ key: '心率', name: '心率' },
				{ key: '体重', name: '体重' },
				{ key: '体温', name: '体温' }
			],
			timeOptions: [
				{ label: '最近一周', value: 7 },
				{ label: '最近一月', value: 30 },
				{ label: '最近三月', value: 90 },
				{ label: '最近一年', value: 365 },
				{ label: '全部时间', value: 0 }
			],
			sortOptions: [
				{ key: 'time', name: '时间' },
				{ key: 'type', name: '类型' },
				{ key: 'status', name: '状态' }
			],
			stats: {
				total: 156,
				normal: 128,
				warning: 23,
				abnormal: 5
			},
			historyRecords: [
				{
					id: 1,
					type: '血压',
					value: '135/85 mmHg',
					status: 'warning',
					day: '18',
					month: '01月',
					time: '08:30',
					note: '晨起测量'
				},
				{
					id: 2,
					type: '血糖',
					value: '5.8 mmol/L',
					status: 'normal',
					day: '18',
					month: '01月',
					time: '07:45',
					note: '空腹血糖'
				},
				{
					id: 3,
					type: '体重',
					value: '65.2 kg',
					status: 'normal',
					day: '17',
					month: '01月',
					time: '19:00',
					note: ''
				},
				{
					id: 4,
					type: '心率',
					value: '78 bpm',
					status: 'normal',
					day: '17',
					month: '01月',
					time: '08:15',
					note: '静息心率'
				},
				{
					id: 5,
					type: '血压',
					value: '142/92 mmHg',
					status: 'danger',
					day: '16',
					month: '01月',
					time: '20:30',
					note: '晚餐后测量'
				}
			]
		}
	},
	onLoad(options) {
		if (options.type) {
			this.activeType = options.type
		}
		this.loadHistoryData()
	},
	methods: {
		selectType(type) {
			this.activeType = type
			this.refreshData()
		},
		onTimeChange(e) {
			this.timeIndex = e.detail.value
			this.refreshData()
		},
		selectSort(sort) {
			this.sortBy = sort
			this.refreshData()
		},
		getStatusText(status) {
			const statusMap = {
				'normal': '正常',
				'warning': '偏高',
				'danger': '异常'
			}
			return statusMap[status] || '未知'
		},
		loadHistoryData() {
			// 模拟加载数据
			console.log('加载历史数据')
		},
		refreshData() {
			this.refreshing = true
			this.page = 1
			this.hasMore = true
			// 模拟刷新数据
			setTimeout(() => {
				this.refreshing = false
			}, 1000)
		},
		loadMore() {
			if (this.hasMore) {
				this.page++
				// 模拟加载更多数据
				console.log('加载更多数据')
			}
		},
		viewDetail(record) {
			uni.navigateTo({
				url: `/pages/health/record-detail?id=${record.id}`
			})
		},
		editRecord(record) {
			uni.navigateTo({
				url: `/pages/health/record?id=${record.id}&mode=edit`
			})
		},
		deleteRecord(record) {
			uni.showModal({
				title: '确认删除',
				content: `确定要删除这条${record.type}记录吗？`,
				success: (res) => {
					if (res.confirm) {
						const index = this.historyRecords.findIndex(r => r.id === record.id)
						if (index > -1) {
							this.historyRecords.splice(index, 1)
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							})
						}
					}
				}
			})
		},
		addRecord() {
			uni.navigateTo({
				url: `/pages/health/record?type=${this.activeType === 'all' ? '' : this.activeType}`
			})
		}
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background: #f5f5f5;
}

.content {
	padding: 140rpx 32rpx 40rpx;
}

.filter-section {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.filter-tabs {
	display: flex;
	gap: 16rpx;
	margin-bottom: 20rpx;
	overflow-x: auto;
}

.filter-tab {
	font-size: 26rpx;
	color: #666;
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	background: #f5f5f5;
	white-space: nowrap;
}

.filter-tab.active {
	background: #ff8a00;
	color: white;
}

.time-filter {
	display: flex;
	justify-content: flex-end;
}

.picker-display {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 8rpx 16rpx;
	background: #f5f5f5;
	border-radius: 8rpx;
}

.picker-text {
	font-size: 26rpx;
	color: #333;
}

.stats-section {
	margin-bottom: 24rpx;
}

.stats-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	display: flex;
	justify-content: space-around;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.stat-item {
	text-align: center;
}

.stat-value {
	font-size: 32rpx;
	font-weight: 600;
	color: #ff8a00;
	display: block;
	margin-bottom: 8rpx;
}

.stat-label {
	font-size: 24rpx;
	color: #666;
}

.history-section {
	flex: 1;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.sort-options {
	display: flex;
	gap: 16rpx;
}

.sort-item {
	font-size: 24rpx;
	color: #666;
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
	background: #f5f5f5;
}

.sort-item.active {
	background: #ff8a00;
	color: white;
}

.history-list {
	flex: 1;
	min-height: 0;
}

.history-item {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 16rpx;
	display: flex;
	align-items: center;
	gap: 16rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.record-date {
	text-align: center;
	min-width: 80rpx;
}

.date-day {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	display: block;
}

.date-month {
	font-size: 22rpx;
	color: #999;
	display: block;
	margin-bottom: 4rpx;
}

.date-time {
	font-size: 20rpx;
	color: #ccc;
	display: block;
}

.record-content {
	flex: 1;
}

.record-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8rpx;
}

.record-type {
	font-size: 26rpx;
	color: #666;
}

.record-status {
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-size: 22rpx;
}

.record-status.normal {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.record-status.warning {
	background: rgba(255, 152, 0, 0.1);
	color: #ff9800;
}

.record-status.danger {
	background: rgba(244, 67, 54, 0.1);
	color: #f44336;
}

.record-value {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 4rpx;
}

.record-note {
	font-size: 24rpx;
	color: #999;
	display: block;
}

.record-actions {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.action-btn {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	border: none;
	background: #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: center;
}

.load-more {
	text-align: center;
	padding: 32rpx;
}

.load-text {
	font-size: 26rpx;
	color: #999;
}

.no-more {
	text-align: center;
	padding: 32rpx;
}

.no-more-text {
	font-size: 26rpx;
	color: #ccc;
}

.empty-state {
	text-align: center;
	padding: 80rpx 32rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
	display: block;
	margin: 24rpx 0 32rpx;
}

.fab {
	position: fixed;
	bottom: 120rpx;
	right: 32rpx;
	width: 96rpx;
	height: 96rpx;
	border-radius: 50%;
	background: #ff8a00;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 24rpx rgba(255, 138, 0, 0.3);
}
</style>
