/**
 * iOS风格适老化工具类
 * 在iOS设计规范基础上优化适老化体验
 * 确保老年用户的易用性和现代感并存
 */

// iOS风格适老化配置
export const IOS_ELDERLY_CONFIG = {
  // 字体系统 - 基于iOS Typography
  typography: {
    // 字体放大倍数（保持iOS比例关系）
    scaleRatio: 1.3,
    // 最小字体大小（确保可读性）
    minFontSize: 32, // rpx
    // 字体粗细增强
    fontWeight: {
      regular: 500,   // iOS Medium
      medium: 600,    // iOS Semibold  
      bold: 700       // iOS Bold
    },
    // 行高增强（提升可读性）
    lineHeight: 1.6
  },

  // 颜色系统 - iOS风格高对比度
  colors: {
    // 文字颜色（更高对比度）
    text: {
      primary: '#000000',      // 纯黑文字
      secondary: '#2c2c2e',    // iOS深灰
      tertiary: '#48484a'      // iOS中灰
    },
    // 背景颜色（保持iOS风格）
    background: {
      primary: '#ffffff',      // 纯白背景
      secondary: '#f2f2f7',    // iOS浅灰背景
      card: '#ffffff'          // 卡片背景
    },
    // 边框颜色（增强可见性）
    border: {
      primary: '#8e8e93',      // iOS标准边框
      secondary: '#c7c7cc',    // iOS浅边框
      focus: '#ff8a00'         // 焦点边框（品牌色）
    },
    // 状态颜色（保持iOS系统色）
    status: {
      success: '#34c759',      // iOS绿色
      warning: '#ff9500',      // iOS橙色
      error: '#ff3b30',        // iOS红色
      info: '#007aff'          // iOS蓝色
    }
  },

  // 间距系统 - iOS风格放大
  spacing: {
    // 基础间距放大倍数
    scaleRatio: 1.4,
    // 最小触摸目标（iOS推荐44pt = 88rpx）
    minTouchTarget: 88,
    // 内边距增强
    padding: {
      xs: 12,   // 6 * 1.4 = 8.4 ≈ 12
      sm: 17,   // 12 * 1.4 = 16.8 ≈ 17
      md: 22,   // 16 * 1.4 = 22.4 ≈ 22
      lg: 34,   // 24 * 1.4 = 33.6 ≈ 34
      xl: 45    // 32 * 1.4 = 44.8 ≈ 45
    },
    // 外边距增强
    margin: {
      xs: 8,    // 4 * 1.4 = 5.6 ≈ 8
      sm: 11,   // 8 * 1.4 = 11.2 ≈ 11
      md: 17,   // 12 * 1.4 = 16.8 ≈ 17
      lg: 22,   // 16 * 1.4 = 22.4 ≈ 22
      xl: 34    // 24 * 1.4 = 33.6 ≈ 34
    }
  },

  // 圆角系统 - iOS风格适度增大
  borderRadius: {
    xs: 10,   // 8 * 1.25 = 10
    sm: 15,   // 12 * 1.25 = 15
    md: 20,   // 16 * 1.25 = 20
    lg: 25,   // 20 * 1.25 = 25
    xl: 30    // 24 * 1.25 = 30
  },

  // 阴影系统 - iOS风格增强
  shadows: {
    light: '0 2rpx 8rpx rgba(0, 0, 0, 0.06)',
    medium: '0 4rpx 16rpx rgba(0, 0, 0, 0.1)',
    heavy: '0 8rpx 24rpx rgba(0, 0, 0, 0.15)',
    focus: '0 0 0 4rpx rgba(255, 138, 0, 0.3)' // 焦点阴影
  },

  // 动画系统 - 适老化调整
  animations: {
    // 动画时长（稍微延长）
    duration: {
      fast: 200,     // 150 * 1.33 ≈ 200
      standard: 350, // 250 * 1.4 = 350
      slow: 500      // 350 * 1.43 ≈ 500
    },
    // 缓动函数（保持iOS风格）
    easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    // 减少复杂动画
    reduceMotion: true
  },

  // 交互反馈 - 适老化增强
  feedback: {
    // 震动强度
    vibration: {
      light: 'medium',  // 轻触使用中等强度
      medium: 'heavy',  // 中等使用重度
      heavy: 'heavy'    // 重度保持重度
    },
    // 语音播报
    voice: {
      enabled: true,
      volume: 0.6,      // 适中音量
      speed: 0.7        // 较慢语速
    },
    // 提示时长
    toastDuration: {
      short: 3000,      // 3秒
      long: 5000        // 5秒
    }
  }
}

/**
 * 适老化模式管理类
 */
export class ElderlyModeManager {
  constructor() {
    this.isEnabled = false
    this.settings = {}
    this.init()
  }

  /**
   * 初始化适老化模式
   */
  init() {
    try {
      this.isEnabled = uni.getStorageSync('elderlyMode') || false
      this.settings = uni.getStorageSync('elderlySettings') || {}
      
      if (this.isEnabled) {
        this.applyElderlyStyles()
      }
    } catch (error) {
      console.error('适老化模式初始化失败:', error)
    }
  }

  /**
   * 启用适老化模式
   */
  enable() {
    this.isEnabled = true
    uni.setStorageSync('elderlyMode', true)
    this.applyElderlyStyles()
    this.showModeChangeNotification('已开启适老版')
  }

  /**
   * 禁用适老化模式
   */
  disable() {
    this.isEnabled = false
    uni.setStorageSync('elderlyMode', false)
    this.removeElderlyStyles()
    this.showModeChangeNotification('已关闭适老版')
  }

  /**
   * 切换适老化模式
   */
  toggle() {
    if (this.isEnabled) {
      this.disable()
    } else {
      this.enable()
    }
    return this.isEnabled
  }

  /**
   * 应用适老化样式
   */
  applyElderlyStyles() {
    const config = IOS_ELDERLY_CONFIG
    
    // 设置CSS变量
    const root = document.documentElement || document.body
    if (root && root.style) {
      // 字体系统
      root.style.setProperty('--elderly-font-scale', config.typography.scaleRatio)
      root.style.setProperty('--elderly-line-height', config.typography.lineHeight)
      
      // 颜色系统
      root.style.setProperty('--elderly-text-primary', config.colors.text.primary)
      root.style.setProperty('--elderly-bg-primary', config.colors.background.primary)
      root.style.setProperty('--elderly-border-primary', config.colors.border.primary)
      
      // 间距系统
      root.style.setProperty('--elderly-spacing-scale', config.spacing.scaleRatio)
      root.style.setProperty('--elderly-touch-target', `${config.spacing.minTouchTarget}rpx`)
    }

    // 添加全局适老化类
    const body = document.body
    if (body) {
      body.classList.add('ios-elderly-mode')
    }
  }

  /**
   * 移除适老化样式
   */
  removeElderlyStyles() {
    const body = document.body
    if (body) {
      body.classList.remove('ios-elderly-mode')
    }
  }

  /**
   * 显示模式切换通知
   */
  showModeChangeNotification(message) {
    const config = IOS_ELDERLY_CONFIG.feedback
    
    uni.showToast({
      title: message,
      icon: 'success',
      duration: config.toastDuration.short
    })

    // 语音播报（如果启用）
    if (config.voice.enabled) {
      this.speakText(message)
    }

    // 震动反馈
    try {
      uni.vibrateShort({
        type: config.vibration.medium
      })
    } catch (error) {
      console.log('震动反馈不可用')
    }
  }

  /**
   * 语音播报
   */
  speakText(text) {
    // #ifdef APP-PLUS
    try {
      if (plus.speech) {
        const config = IOS_ELDERLY_CONFIG.feedback.voice
        plus.speech.startSpeech({
          content: text,
          volume: config.volume,
          speed: config.speed
        })
      }
    } catch (error) {
      console.log('语音播报失败:', error)
    }
    // #endif
  }

  /**
   * 获取适老化配置
   */
  getConfig() {
    return IOS_ELDERLY_CONFIG
  }

  /**
   * 检查是否启用
   */
  isElderlyMode() {
    return this.isEnabled
  }

  /**
   * 获取适老化字体大小
   */
  getElderlyFontSize(baseFontSize) {
    if (!this.isEnabled) return baseFontSize
    
    const config = IOS_ELDERLY_CONFIG.typography
    const scaledSize = baseFontSize * config.scaleRatio
    
    // 确保不小于最小字体大小
    return Math.max(scaledSize, config.minFontSize)
  }

  /**
   * 获取适老化间距
   */
  getElderlySpacing(baseSpacing) {
    if (!this.isEnabled) return baseSpacing
    
    return baseSpacing * IOS_ELDERLY_CONFIG.spacing.scaleRatio
  }

  /**
   * 获取适老化触摸目标大小
   */
  getElderlyTouchTarget(baseSize) {
    if (!this.isEnabled) return baseSize
    
    const minTarget = IOS_ELDERLY_CONFIG.spacing.minTouchTarget
    return Math.max(baseSize, minTarget)
  }
}

// 创建全局适老化管理器实例
export const elderlyModeManager = new ElderlyModeManager()

// 导出工具函数
export const ElderlyUtils = {
  /**
   * 检查是否为适老化模式
   */
  isElderlyMode() {
    return elderlyModeManager.isElderlyMode()
  },

  /**
   * 获取适老化配置
   */
  getConfig() {
    return IOS_ELDERLY_CONFIG
  },

  /**
   * 应用适老化样式到元素
   */
  applyElderlyStyle(element, styles) {
    if (!this.isElderlyMode() || !element) return

    Object.keys(styles).forEach(key => {
      element.style[key] = styles[key]
    })
  },

  /**
   * 获取适老化类名
   */
  getElderlyClass(baseClass = '') {
    return this.isElderlyMode() ? `${baseClass} ios-elderly-mode` : baseClass
  }
}

// 导出默认对象
export default {
  IOS_ELDERLY_CONFIG,
  ElderlyModeManager,
  elderlyModeManager,
  ElderlyUtils
}
