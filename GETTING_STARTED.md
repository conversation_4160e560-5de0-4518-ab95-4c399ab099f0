# 智慧养老项目启动指南

## 项目概述

这是一个完整的智慧养老 uni-app 项目，已经完成了基础架构搭建和核心功能实现。项目包含了完整的组件库、工具类、页面结构和数据管理系统。

## 🚀 快速开始

### 1. 环境准备

确保您的开发环境已安装：
- **HBuilderX** 3.0 或更高版本
- **Node.js** 14.0 或更高版本
- **微信开发者工具**（如需开发小程序）

### 2. 项目导入

1. 打开 HBuilderX
2. 选择 "文件" → "导入" → "从本地目录导入"
3. 选择项目根目录
4. 等待项目加载完成

### 3. 运行项目

#### 运行到浏览器（H5）
1. 在 HBuilderX 中右键点击项目根目录
2. 选择 "运行" → "运行到浏览器" → "Chrome"
3. 项目将自动编译并在浏览器中打开

#### 运行到微信小程序
1. 确保已安装微信开发者工具
2. 在 HBuilderX 中选择 "运行" → "运行到小程序模拟器" → "微信开发者工具"
3. 首次运行需要配置微信开发者工具路径

#### 运行到手机App
1. 选择 "运行" → "运行到手机或模拟器"
2. 选择对应的设备或模拟器

## 📱 项目功能验证

### 1. 首页功能
- 访问首页查看轮播图和快捷入口
- 测试各个功能模块的跳转

### 2. 底部导航
项目配置了4个主要页面的底部导航：
- **首页** (`/pages/home/<USER>
- **工作台** (`/pages/workspace/workspace`)
- **地图** (`/pages/map/map`)
- **我的** (`/pages/profile/profile`)

### 3. 组件测试
访问测试页面验证所有组件功能：
```
/pages/test/icons
```

### 4. 核心功能测试
- **机构查找**: `/pages/institution/list`
- **服务预约**: `/pages/service/list`
- **补贴申请**: `/pages/subsidy/list`
- **个人中心**: `/pages/profile/profile`

## 🔧 开发配置

### 1. 应用配置
主要配置文件：
- `manifest.json` - 应用基本信息和平台配置
- `pages.json` - 页面路由和导航配置
- `App.vue` - 全局样式和应用生命周期

### 2. 组件库
项目包含完整的组件库，位于 `components/` 目录：

```
components/
├── Icon/                # 图标组件（基于emoji）
├── LoadingSkeleton/     # 骨架屏组件
├── ErrorBoundary/       # 错误边界组件
├── LazyImage/          # 懒加载图片组件
├── InteractiveCard/    # 交互卡片组件
├── InteractiveButton/  # 交互按钮组件
├── FormBuilder/        # 表单构建器组件
└── PageHeader/         # 页面头部组件
```

### 3. 工具类
项目提供了三个核心工具类：

```javascript
// 反馈工具类
import FeedbackUtils from '@/utils/feedback.js'

// 离线数据管理
import OfflineDataManager from '@/utils/offlineData.js'

// 模拟API数据
import MockAPI from '@/utils/mockData.js'
```

## 📊 数据管理

### 1. 离线数据
项目内置了完整的离线数据系统，包括：
- 养老机构数据
- 服务项目数据
- 补贴政策数据
- 新闻资讯数据
- 用户信息数据

### 2. 模拟API
提供了完整的模拟API接口，支持：
- 分页查询
- 条件筛选
- 数据提交
- 用户认证

### 3. 本地存储
使用 uni-app 的存储API进行数据持久化：
```javascript
// 存储数据
uni.setStorageSync('key', value)

// 获取数据
const value = uni.getStorageSync('key')
```

## 🎨 UI设计特色

### 1. 适老化设计
- 大字体支持
- 高对比度色彩
- 简化的操作流程
- 清晰的视觉反馈

### 2. 响应式布局
- 支持多种屏幕尺寸
- 自适应组件设计
- 灵活的栅格系统

### 3. 交互体验
- 流畅的动画效果
- 触觉反馈支持
- 加载状态提示
- 错误处理机制

## 🔨 自定义开发

### 1. 添加新页面
1. 在 `pages/` 目录下创建新的页面文件
2. 在 `pages.json` 中添加页面配置
3. 配置页面样式和导航

### 2. 创建新组件
1. 在 `components/` 目录下创建组件文件夹
2. 编写组件代码
3. 在 `main.js` 中注册全局组件（可选）

### 3. 扩展工具类
1. 在 `utils/` 目录下创建新的工具文件
2. 导出相应的方法和类
3. 在需要的地方导入使用

## 📦 打包发布

### 1. H5版本
1. 选择 "发行" → "网站-H5手机版"
2. 配置发行参数
3. 点击发行，生成 dist 目录
4. 将 dist 目录部署到服务器

### 2. 小程序版本
1. 选择 "发行" → "小程序-微信"
2. 配置小程序信息
3. 生成小程序代码包
4. 在微信开发者工具中上传审核

### 3. App版本
1. 选择 "发行" → "原生App-云打包"
2. 配置打包参数
3. 提交云打包
4. 下载生成的安装包

## ⚠️ 注意事项

### 1. 图标资源
当前项目使用emoji作为图标，生产环境建议：
- 替换为真实的图标文件
- 使用字体图标或SVG图标
- 确保图标在各平台显示一致

### 2. API接口
当前使用模拟数据，实际部署时需要：
- 替换为真实的后端API接口
- 配置正确的服务器地址
- 处理网络请求和错误

### 3. 地图功能
地图相关功能需要：
- 申请地图服务的API密钥
- 配置相应的权限
- 测试定位和导航功能

### 4. 权限配置
根据功能需求配置：
- 位置权限（地图功能）
- 相机权限（头像上传）
- 存储权限（文件下载）
- 通讯录权限（联系人功能）

## 🆘 常见问题

### Q: 项目运行时出现组件找不到的错误？
A: 检查组件路径是否正确，确保在 `main.js` 中正确注册了全局组件。

### Q: 页面跳转失败？
A: 检查 `pages.json` 中是否正确配置了页面路径。

### Q: 样式显示异常？
A: 检查CSS单位是否使用了 `rpx`，确保样式兼容多端。

### Q: 数据加载失败？
A: 检查网络请求配置，确认API接口地址正确。

## 📞 技术支持

如遇到问题，可以：
1. 查看项目内的测试页面和示例代码
2. 参考 uni-app 官方文档
3. 检查浏览器控制台的错误信息
4. 查看 HBuilderX 的控制台输出

## 🎯 下一步计划

建议的开发优先级：
1. 替换模拟数据为真实API
2. 完善用户认证系统
3. 添加支付功能
4. 优化性能和用户体验
5. 添加更多适老化功能

---

祝您开发顺利！🎉
